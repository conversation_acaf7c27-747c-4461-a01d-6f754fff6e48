from datetime import datetime, timedelta, date
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.db_operations import (
    AccountOperations,
    FixedExpenseOperations,
    MiscExpenseOperations,
    PayrollExpenseOperations,
    ProjectOperations,
    PurchaseOrderOperations,
    InvoiceOperations,
)
from app.cash_flow.models import (
    ForecastResponse,
)
from app.cash_flow.models.utils import to_date_only
from app.cash_flow.services.project_utils import create_project_response


class ForecastService:
    def __init__(self, session: AsyncSession):
        self.account_ops = AccountOperations(session)
        self.fixed_expense_ops = FixedExpenseOperations(session)
        self.misc_expense_ops = MiscExpenseOperations(session)
        self.payroll_expense_ops = PayrollExpenseOperations(session)
        self.project_ops = ProjectOperations(session)
        self.purchase_order_ops = PurchaseOrderOperations(session)
        self.invoice_ops = InvoiceOperations(session)

    def _ensure_naive_datetime(self, dt: datetime) -> datetime:
        """Ensure datetime object is naive (no timezone info)."""
        if dt is None:
            return dt
        if dt.tzinfo is not None:
            return dt.replace(tzinfo=None)
        return dt

    def _get_nth_weekday_of_month(
        self, year: int, month: int, week_of_month: int, day_of_week: int
    ) -> date:
        """
        Get the date of the nth occurrence of a weekday in a month.

        Args:
            year: Year
            month: Month (1-12)
            week_of_month: Week of the month (1-4)
            day_of_week: Day of the week (0=Monday, 6=Sunday)

        Returns:
            date object for the specific date, or None if it doesn't exist
        """
        # Get the first day of the month
        first_day = datetime(year, month, 1)

        # Find the first occurrence of the target weekday
        days_ahead = day_of_week - first_day.weekday()
        if days_ahead < 0:  # Target day already happened this week
            days_ahead += 7

        first_occurrence = first_day + timedelta(days=days_ahead)

        # Calculate the nth occurrence
        target_date = first_occurrence + timedelta(weeks=week_of_month - 1)

        # Check if the target date is still in the same month
        if target_date.month != month:
            return None

        return target_date.date()

    def _get_week_of_month(self, date: date) -> int:
        """
        Get the week of the month for a given date.

        Args:
            date: The date to check

        Returns:
            Week of the month (1-4)
        """
        # Get the first day of the month
        first_day = date.replace(day=1)

        # Calculate which week of the month this date falls in
        # Week 1 starts on the first day of the month
        days_from_first = (date - first_day).days
        week_of_month = (days_from_first // 7) + 1

        # Cap at week 4 for months with 5+ weeks
        return min(week_of_month, 4)

    def _get_fridays_of_month(self, year: int, month: int) -> list[date]:
        """
        Get all Fridays in a given month.

        Args:
            year: The year
            month: The month

        Returns:
            List of date objects for all Fridays in the month
        """
        first_day = datetime(year, month, 1)
        # Find the first Friday of the month
        first_friday = first_day + timedelta(days=(4 - first_day.weekday()) % 7)

        fridays = []
        current_friday = first_friday

        # Collect all Fridays in the month
        while current_friday.month == month:
            fridays.append(current_friday.date())
            current_friday += timedelta(days=7)

        return fridays

    def _calculate_payroll_amount(
        self,
        expense,
        week_start: date,
        week_end: date,
        is_first_occurrence: bool = False,
    ) -> float:
        """
        Calculate the payroll amount for a given week, handling partial pay periods.

        Args:
            expense: PayrollExpenseTable object
            week_start: Start of the week (date only)
            week_end: End of the week (date only)
            is_first_occurrence: Whether this is the first payroll occurrence after the expense date

        Returns:
            float: The calculated payroll amount
        """
        base_amount = float(expense.amount)

        # If this is not the first occurrence, return the full amount
        if not is_first_occurrence:
            return base_amount

        # For the first occurrence, calculate prorated amount based on pay period
        expense_date = to_date_only(expense.date)

        if not expense.recurrence:
            return base_amount

        # Calculate days in a pay period
        if expense.recurrence.lower() == "weekly":
            pay_period_days = 7
        elif expense.recurrence.lower() in ["bi-weekly", "biweekly"]:
            pay_period_days = 14
        elif expense.recurrence.lower() == "monthly":
            pay_period_days = 30  # Approximate
        elif expense.recurrence.lower() == "1st & 3rd fridays":
            pay_period_days = 14  # Approximately 2 weeks between 1st and 3rd Friday
        elif expense.recurrence.lower() == "2nd & 4th fridays":
            pay_period_days = 14  # Approximately 2 weeks between 2nd and 4th Friday
        else:
            return base_amount

        # Find the first payroll date after the expense date
        if expense.recurrence.lower() == "1st & 3rd fridays":
            # Find the first 1st or 3rd Friday on or after the expense date
            current_month = expense_date.month
            current_year = expense_date.year

            # Get all Fridays in current month
            fridays = self._get_fridays_of_month(current_year, current_month)

            # Find the first occurrence on or after expense date
            first_payroll_date = None

            # Check 1st Friday (index 0)
            if len(fridays) > 0 and fridays[0] >= expense_date:
                first_payroll_date = fridays[0]
            # Check 3rd Friday (index 2)
            elif len(fridays) > 2 and fridays[2] >= expense_date:
                first_payroll_date = fridays[2]
            else:
                # Move to next month's 1st Friday
                next_month = current_month + 1
                next_year = current_year
                if next_month > 12:
                    next_month = 1
                    next_year += 1

                next_fridays = self._get_fridays_of_month(next_year, next_month)
                if len(next_fridays) > 0:
                    first_payroll_date = next_fridays[0]
        elif expense.recurrence.lower() == "2nd & 4th fridays":
            # Find the first 2nd or 4th Friday on or after the expense date
            current_month = expense_date.month
            current_year = expense_date.year

            # Get all Fridays in current month
            fridays = self._get_fridays_of_month(current_year, current_month)

            # Find the first occurrence on or after expense date
            first_payroll_date = None

            # Check 2nd Friday (index 1)
            if len(fridays) > 1 and fridays[1] >= expense_date:
                first_payroll_date = fridays[1]
            # Check 4th Friday (index 3)
            elif len(fridays) > 3 and fridays[3] >= expense_date:
                first_payroll_date = fridays[3]
            else:
                # Move to next month's 2nd Friday
                next_month = current_month + 1
                next_year = current_year
                if next_month > 12:
                    next_month = 1
                    next_year += 1

                next_fridays = self._get_fridays_of_month(next_year, next_month)
                if len(next_fridays) > 1:
                    first_payroll_date = next_fridays[1]
        else:
            # Fallback: use the original expense date logic
            first_payroll_date = expense_date

        if not first_payroll_date:
            return base_amount

        # Check if this week contains the first payroll date
        if not (week_start <= first_payroll_date <= week_end):
            return base_amount

        # Calculate days from expense start to first payroll date
        days_from_start = (
            first_payroll_date - expense_date
        ).days + 1  # +1 to include the start date

        # Calculate prorated amount
        if days_from_start >= pay_period_days:
            return base_amount
        else:
            return base_amount * (days_from_start / pay_period_days)

    def _should_apply_recurring_expense(
        self, expense, week_start: date, week_end: date
    ):
        """
        Helper function to determine if a recurring expense should be applied in a given week
        based on its recurrence pattern.
        """
        # Convert to date objects for date-only comparisons
        expense_date = to_date_only(expense.date)

        # Check if the week is on or after the expense date
        if week_end < expense_date:
            return False

        if not expense.recurrence:
            # Non-recurring: apply only if date falls within the week
            return expense_date >= week_start and expense_date <= week_end

        if expense.recurrence.lower() == "weekly":
            # Apply every week
            return True

        if expense.recurrence.lower() in ["bi-weekly", "biweekly"]:
            # Apply every 2 weeks
            weeks_since_start = (week_start - expense_date).days // 7
            return weeks_since_start % 2 == 0

        if expense.recurrence.lower() == "monthly":
            # Apply monthly on approximately the same day
            days_diff = (week_start - expense_date).days
            return (
                days_diff % 30 < 7
                and expense_date.day <= week_start.day <= expense_date.day + 6
            )

        if expense.recurrence.lower() == "quarterly":
            # Apply every 3 months on approximately the same day
            days_diff = (week_start - expense_date).days
            return (
                days_diff % 90 < 7
                and expense_date.day <= week_start.day <= expense_date.day + 6
            )

        if expense.recurrence.lower() == "1st & 3rd fridays":
            # Apply on 1st and 3rd Fridays of each month
            # In months with 5 Fridays, also apply on 5th Friday (every other Friday pattern)

            # Check both the start month and end month (in case week spans months)
            months_to_check = [(week_start.year, week_start.month)]
            if week_end.month != week_start.month:
                months_to_check.append((week_end.year, week_end.month))

            for year, month in months_to_check:
                fridays = self._get_fridays_of_month(year, month)

                # Check if this week contains the 1st Friday (index 0)
                if len(fridays) > 0 and week_start <= fridays[0] <= week_end:
                    return True

                # Check if this week contains the 3rd Friday (index 2)
                if len(fridays) > 2 and week_start <= fridays[2] <= week_end:
                    return True

                # Check if this week contains the 5th Friday (index 4) - for months with 5 Fridays
                if len(fridays) > 4 and week_start <= fridays[4] <= week_end:
                    return True

            return False

        if expense.recurrence.lower() == "2nd & 4th fridays":
            # Apply on 2nd and 4th Fridays of each month

            # Check both the start month and end month (in case week spans months)
            months_to_check = [(week_start.year, week_start.month)]
            if week_end.month != week_start.month:
                months_to_check.append((week_end.year, week_end.month))

            for year, month in months_to_check:
                fridays = self._get_fridays_of_month(year, month)

                # Check if this week contains the 2nd Friday (index 1)
                if len(fridays) > 1 and week_start <= fridays[1] <= week_end:
                    return True

                # Check if this week contains the 4th Friday (index 3)
                if len(fridays) > 3 and week_start <= fridays[3] <= week_end:
                    return True

            return False

        if expense.recurrence.lower() == "custom" and expense.recurrence_interval:
            # Apply based on custom interval in days
            days_since_start = (week_start - expense_date).days
            return days_since_start % expense.recurrence_interval == 0

        return False

    # async def _calculate_historical_totals(
    #     self, organization_id: int, start_date: datetime
    # ) -> tuple[float, float, float]:
    #     """
    #     Calculate cumulative totals for all expenses, invoices, and purchase orders
    #     that occurred before the forecast start date using the same logic as the forecast.

    #     Returns:
    #         tuple: (payroll_total, fixed_total, misc_total)
    #     """
    #     # Get all data
    #     fixed_expenses = await self.fixed_expense_ops.get_fixed_expenses(
    #         organization_id
    #     )
    #     misc_expenses = await self.misc_expense_ops.get_misc_expenses(organization_id)
    #     payroll_expenses = await self.payroll_expense_ops.get_payroll_expenses(
    #         organization_id
    #     )

    #     payroll_total = 0.0
    #     fixed_total = 0.0
    #     misc_total = 0.0

    #     start_date_only = to_date_only(start_date)

    #     # Find the earliest expense date to start simulation from
    #     earliest_date = start_date_only
    #     for expense in payroll_expenses + fixed_expenses + misc_expenses:
    #         expense_date = to_date_only(expense.date)
    #         if expense_date < earliest_date:
    #             earliest_date = expense_date

    #     # If no expenses before start date, return zeros
    #     if earliest_date >= start_date_only:
    #         return payroll_total, fixed_total, misc_total

    #     # Simulate week by week from earliest expense date to start date
    #     # Start from the Sunday of the week containing the earliest date
    #     current_week_start = earliest_date - timedelta(days=earliest_date.weekday() + 1)
    #     if earliest_date.weekday() == 6:  # If Sunday, start from that day
    #         current_week_start = earliest_date

    #     while current_week_start < start_date_only:
    #         week_end = current_week_start + timedelta(days=6)

    #         # Process payroll expenses for this week
    #         for expense in payroll_expenses:
    #             if self._should_apply_recurring_expense(
    #                 expense, current_week_start, week_end
    #             ):
    #                 # Check if this is the first occurrence for prorated calculation
    #                 expense_date = to_date_only(expense.date)
    #                 is_first_occurrence = False

    #                 # Determine if this is the first payroll occurrence after the expense date
    #                 if expense.recurrence and expense.recurrence.lower() in [
    #                     "weekly",
    #                     "bi-weekly",
    #                     "biweekly",
    #                     "monthly",
    #                     "1st & 3rd fridays",
    #                 ]:
    #                     # Use the same logic as in the main forecast processing
    #                     if (
    #                         hasattr(expense, "week_of_month")
    #                         and hasattr(expense, "day_of_week")
    #                         and expense.week_of_month is not None
    #                         and expense.day_of_week is not None
    #                     ):
    #                         # For expenses with specific scheduling, check if this is the first occurrence
    #                         if expense.recurrence.lower() == "monthly":
    #                             first_payroll_date = self._get_nth_weekday_of_month(
    #                                 expense_date.year,
    #                                 expense_date.month,
    #                                 expense.week_of_month,
    #                                 expense.day_of_week,
    #                             )
    #                             if (
    #                                 first_payroll_date
    #                                 and first_payroll_date < expense_date
    #                             ):
    #                                 next_month = expense_date.month + 1
    #                                 next_year = expense_date.year
    #                                 if next_month > 12:
    #                                     next_month = 1
    #                                     next_year += 1
    #                                 first_payroll_date = self._get_nth_weekday_of_month(
    #                                     next_year,
    #                                     next_month,
    #                                     expense.week_of_month,
    #                                     expense.day_of_week,
    #                                 )
    #                             is_first_occurrence = (
    #                                 first_payroll_date
    #                                 and current_week_start
    #                                 <= first_payroll_date
    #                                 <= week_end
    #                             )
    #                         elif expense.recurrence.lower() in [
    #                             "bi-weekly",
    #                             "biweekly",
    #                         ]:
    #                             first_payroll_date = self._get_nth_weekday_of_month(
    #                                 expense_date.year,
    #                                 expense_date.month,
    #                                 expense.week_of_month,
    #                                 expense.day_of_week,
    #                             )
    #                             if (
    #                                 first_payroll_date
    #                                 and first_payroll_date < expense_date
    #                             ):
    #                                 next_month = expense_date.month + 1
    #                                 next_year = expense_date.year
    #                                 if next_month > 12:
    #                                     next_month = 1
    #                                     next_year += 1
    #                                 first_payroll_date = self._get_nth_weekday_of_month(
    #                                     next_year,
    #                                     next_month,
    #                                     expense.week_of_month,
    #                                     expense.day_of_week,
    #                                 )
    #                             is_first_occurrence = (
    #                                 first_payroll_date
    #                                 and current_week_start
    #                                 <= first_payroll_date
    #                                 <= week_end
    #                             )
    #                     elif expense.recurrence.lower() == "1st & 3rd fridays":
    #                         # For 1st & 3rd Fridays, find the first occurrence
    #                         current_month = expense_date.month
    #                         current_year = expense_date.year

    #                         # Get all Fridays in current month
    #                         fridays = self._get_fridays_of_month(
    #                             current_year, current_month
    #                         )

    #                         # Find the first occurrence on or after expense date
    #                         first_payroll_date = None
    #                         if len(fridays) > 0 and fridays[0] >= expense_date:
    #                             first_payroll_date = fridays[0]
    #                         elif len(fridays) > 2 and fridays[2] >= expense_date:
    #                             first_payroll_date = fridays[2]
    #                         else:
    #                             # Move to next month's 1st Friday
    #                             next_month = current_month + 1
    #                             next_year = current_year
    #                             if next_month > 12:
    #                                 next_month = 1
    #                                 next_year += 1

    #                             next_fridays = self._get_fridays_of_month(
    #                                 next_year, next_month
    #                             )
    #                             if len(next_fridays) > 0:
    #                                 first_payroll_date = next_fridays[0]

    #                         is_first_occurrence = (
    #                             first_payroll_date
    #                             and current_week_start <= first_payroll_date <= week_end
    #                         )
    #                     else:
    #                         # For expenses without specific scheduling, use simpler logic
    #                         is_first_occurrence = (
    #                             current_week_start <= expense_date <= week_end
    #                         )

    #                 # Calculate the payroll amount (potentially prorated for first occurrence)
    #                 amount = self._calculate_payroll_amount(
    #                     expense, current_week_start, week_end, is_first_occurrence
    #                 )
    #                 payroll_total += amount

    #         # Process fixed expenses for this week
    #         for expense in fixed_expenses:
    #             if self._should_apply_recurring_expense(
    #                 expense, current_week_start, week_end
    #             ):
    #                 fixed_total += float(expense.amount)

    #         # Process misc expenses for this week (always one-time)
    #         for expense in misc_expenses:
    #             expense_date = to_date_only(expense.date)
    #             if current_week_start <= expense_date <= week_end:
    #                 misc_total += float(expense.amount)

    #         # Move to next week
    #         current_week_start += timedelta(days=7)

    #     return payroll_total, fixed_total, misc_total

    # async def _calculate_historical_project_data(
    #     self, organization_id: int, start_date: datetime
    # ) -> tuple[float, float, float, float, float, float, float, float, float, float]:
    #     """
    #     Calculate cumulative totals for project milestones, expenses, purchase orders,
    #     and invoices that occurred before the forecast start date.

    #     Returns:
    #         tuple: (
    #             total_current_project_income,
    #             total_anticipated_project_income,
    #             total_current_project_expenses,
    #             total_anticipated_project_expenses,
    #             total_current_project_purchase_orders,
    #             total_anticipated_project_purchase_orders,
    #             total_current_project_invoices,
    #             total_anticipated_project_invoices,
    #         ) - effects on accounts
    #     """
    #     # Get all project data
    #     current_projects = await self.project_ops.get_current_projects(organization_id)
    #     anticipated_projects = await self.project_ops.get_anticipated_projects(
    #         organization_id
    #     )
    #     purchase_orders = await self.purchase_order_ops.get_purchase_orders(
    #         organization_id=organization_id
    #     )
    #     invoices = await self.invoice_ops.get_invoices(organization_id=organization_id)

    #     total_current_project_income = 0.0
    #     total_anticipated_project_income = 0.0
    #     total_current_project_savings = 0.0
    #     total_anticipated_project_savings = 0.0
    #     total_current_project_expenses = 0.0
    #     total_anticipated_project_expenses = 0.0
    #     total_current_project_purchase_orders = 0.0
    #     total_anticipated_project_purchase_orders = 0.0
    #     total_current_project_invoices = 0.0
    #     total_anticipated_project_invoices = 0.0

    #     start_date_only = to_date_only(start_date)
    #     current_project_ids = {proj.id for proj in current_projects}
    #     anticipated_project_ids = {proj.id for proj in anticipated_projects}

    #     # Process current projects
    #     for project in current_projects:
    #         current_project_ids.add(project.id)
    #         # Process milestones (income)
    #         for milestone in project.milestones:
    #             milestone_date = to_date_only(milestone.date)
    #             if milestone_date < start_date_only:
    #                 amount = float(milestone.amount)
    #                 savings_amount = amount * (project.savings_percentage / 100)
    #                 total_current_project_savings += savings_amount
    #                 # Only the non-savings portion affects accounts totals
    #                 total_current_project_income += amount

    #         # Process project expenses
    #         for cost in project.project_expenses:
    #             cost_date = to_date_only(cost.date)
    #             if cost_date < start_date_only:
    #                 total_current_project_expenses += float(cost.amount)

    #     # Process anticipated projects
    #     for project in anticipated_projects:
    #         anticipated_project_ids.add(project.id)
    #         # Process milestones (income)
    #         for milestone in project.milestones:
    #             milestone_date = to_date_only(milestone.date)
    #             if milestone_date < start_date_only:
    #                 amount = float(milestone.amount)
    #                 savings_amount = amount * (project.savings_percentage / 100)
    #                 total_anticipated_project_savings += savings_amount
    #                 total_anticipated_project_income += amount

    #         # Process project expenses
    #         for cost in project.project_expenses:
    #             cost_date = to_date_only(cost.date)
    #             if cost_date < start_date_only:
    #                 total_anticipated_project_expenses += float(cost.amount)

    #     # Process purchase orders
    #     for po in purchase_orders:
    #         if po.due_date:
    #             payment_date = to_date_only(po.due_date)
    #             if payment_date < start_date_only:
    #                 if po.project_id in current_project_ids:
    #                     total_current_project_purchase_orders += float(po.amount)
    #                 elif po.project_id in anticipated_project_ids:
    #                     total_anticipated_project_purchase_orders += float(po.amount)

    #     # Process invoices (income)
    #     for invoice in invoices:
    #         invoice_date = to_date_only(invoice.due_date)
    #         if invoice_date < start_date_only:
    #             if invoice.project_id in current_project_ids:
    #                 total_current_project_invoices += float(invoice.amount)
    #             elif invoice.project_id in anticipated_project_ids:
    #                 total_anticipated_project_invoices += float(invoice.amount)

    #     return (
    #         total_current_project_income,
    #         total_anticipated_project_income,
    #         total_current_project_savings,
    #         total_anticipated_project_savings,
    #         total_current_project_expenses,
    #         total_anticipated_project_expenses,
    #         total_current_project_purchase_orders,
    #         total_anticipated_project_purchase_orders,
    #         total_current_project_invoices,
    #         total_anticipated_project_invoices,
    #     )

    async def generate_forecast(
        self, start_date: datetime, end_date: datetime, organization_id: int
    ) -> ForecastResponse:
        """Generate a new forecast."""
        try:
            # Convert to date-only objects for consistent date handling
            start_date_only = to_date_only(start_date)
            end_date_only = to_date_only(end_date)

            # Create week dates (each week starts on Sunday) - date objects only
            week_dates = []
            current_date = start_date_only
            while current_date <= end_date_only:
                week_dates.append(current_date)
                current_date += timedelta(days=7)

            # Initialize arrays
            num_weeks = len(week_dates)
            working_capital = [0.0] * num_weeks
            savings_balance = [0.0] * num_weeks
            accounts_totals = [0.0] * num_weeks
            payroll_expenses_totals = [0.0] * num_weeks
            payroll_expenses_weekly = [0.0] * num_weeks
            fixed_expenses_totals = [0.0] * num_weeks
            fixed_expenses_weekly = [0.0] * num_weeks
            misc_expenses_totals = [0.0] * num_weeks
            misc_expenses_weekly = [0.0] * num_weeks

            # Initialize project-specific cash flow arrays
            current_projects_expenses = [0.0] * num_weeks
            current_projects_purchase_orders = [0.0] * num_weeks
            current_projects_invoices = [0.0] * num_weeks
            anticipated_projects_expenses = [0.0] * num_weeks
            anticipated_projects_purchase_orders = [0.0] * num_weeks
            anticipated_projects_invoices = [0.0] * num_weeks

            # Initialize project-specific working capital arrays
            current_projects_working_capital = [0.0] * num_weeks
            anticipated_projects_working_capital = [0.0] * num_weeks

            # Get all required data using operations classes
            accounts = await self.account_ops.get_accounts(organization_id)
            fixed_expenses = await self.fixed_expense_ops.get_fixed_expenses(
                organization_id
            )
            misc_expenses = await self.misc_expense_ops.get_misc_expenses(
                organization_id
            )
            payroll_expenses = await self.payroll_expense_ops.get_payroll_expenses(
                organization_id
            )
            current_projects = await self.project_ops.get_current_projects(
                organization_id
            )
            anticipated_projects = await self.project_ops.get_anticipated_projects(
                organization_id
            )
            purchase_orders = await self.purchase_order_ops.get_purchase_orders(
                organization_id=organization_id
            )
            invoices = await self.invoice_ops.get_invoices(organization_id)

            # Calculate historical totals before start date
            # historical_payroll, historical_fixed, historical_misc = (
            #     await self._calculate_historical_totals(organization_id, start_date)
            # )
            # (
            #     historical_current_project_income,
            #     historical_anticipated_project_income,
            #     historical_current_project_savings,
            #     historical_anticipated_project_savings,
            #     historical_current_project_expenses,
            #     historical_anticipated_project_expenses,
            #     historical_current_project_purchase_orders,
            #     historical_anticipated_project_purchase_orders,
            #     historical_current_project_invoices,
            #     historical_anticipated_project_invoices,
            # ) = await self._calculate_historical_project_data(
            #     organization_id, start_date
            # )

            # Initialize balances from accounts
            checking_account = next(
                (acc for acc in accounts if acc.account_type == "Checking"), None
            )
            savings_account = next(
                (acc for acc in accounts if acc.account_type == "Savings"), None
            )
            credit_card_account = next(
                (acc for acc in accounts if acc.account_type == "Credit Card"), None
            )
            line_of_credit_account = next(
                (acc for acc in accounts if acc.account_type == "Line of Credit"), None
            )

            if checking_account:
                working_capital[0] = float(checking_account.balance)
                accounts_totals[0] = float(checking_account.balance)

                # Initialize project cash flow arrays with working capital balance
                current_projects_expenses[0] = float(checking_account.balance)
                current_projects_purchase_orders[0] = float(checking_account.balance)
                current_projects_invoices[0] = float(checking_account.balance)
                anticipated_projects_expenses[0] = float(checking_account.balance)
                anticipated_projects_purchase_orders[0] = float(
                    checking_account.balance
                )
                anticipated_projects_invoices[0] = float(checking_account.balance)

            if credit_card_account:
                amount = float(credit_card_account.balance)
                working_capital[0] -= amount
                accounts_totals[0] -= amount

                # Apply credit card balance to project cash flow arrays
                current_projects_expenses[0] -= amount
                current_projects_purchase_orders[0] -= amount
                current_projects_invoices[0] -= amount
                anticipated_projects_expenses[0] -= amount
                anticipated_projects_purchase_orders[0] -= amount
                anticipated_projects_invoices[0] -= amount

            if line_of_credit_account:
                amount = float(line_of_credit_account.balance)
                working_capital[0] -= amount
                accounts_totals[0] -= amount

                # Apply line of credit balance to project cash flow arrays
                current_projects_expenses[0] -= amount
                current_projects_purchase_orders[0] -= amount
                current_projects_invoices[0] -= amount
                anticipated_projects_expenses[0] -= amount
                anticipated_projects_purchase_orders[0] -= amount
                anticipated_projects_invoices[0] -= amount

            if savings_account:
                savings_balance[0] = float(savings_account.balance)

            # Apply historical project savings to savings balance
            # savings_balance[0] += (
            #     historical_current_project_savings
            #     + historical_anticipated_project_savings
            # )

            # # Apply historical totals to base values
            # payroll_expenses_totals[0] = historical_payroll
            # fixed_expenses_totals[0] = historical_fixed
            # misc_expenses_totals[0] = historical_misc

            # current_projects_expenses[0] += historical_current_project_expenses
            # current_projects_purchase_orders[
            #     0
            # ] -= historical_current_project_purchase_orders
            # current_projects_invoices[0] -= historical_current_project_invoices
            # anticipated_projects_expenses[0] -= historical_anticipated_project_expenses
            # anticipated_projects_purchase_orders[
            #     0
            # ] -= historical_anticipated_project_purchase_orders
            # anticipated_projects_invoices[0] += historical_anticipated_project_invoices

            # historical_expenses = (
            #     historical_payroll - historical_fixed - historical_misc
            # )
            # # Apply historical project data to accounts and cash flow
            # # Net effect = income - (project expenses + purchase orders)
            # historical_net_effect = (
            #     historical_current_project_income
            #     + historical_anticipated_project_income
            #     - historical_current_project_invoices
            #     - historical_anticipated_project_invoices
            #     - historical_expenses
            # )
            # baseline_cash[0] += historical_net_effect
            # accounts_totals[0] += historical_net_effect
            # historical_current_project_net = (
            #     historical_current_project_income - historical_expenses
            # )
            # historical_anticipated_project_net = (
            #     historical_anticipated_project_income - historical_expenses
            # )
            # current_projects_invoices[0] += historical_current_project_net
            # current_projects_expenses[0] += historical_current_project_net
            # current_projects_purchase_orders[0] += historical_current_project_net
            # anticipated_projects_invoices[0] += historical_anticipated_project_net
            # anticipated_projects_expenses[0] += historical_anticipated_project_net
            # anticipated_projects_purchase_orders[
            #     0
            # ] += historical_anticipated_project_net

            # Track project IDs for PO and invoice assignment
            current_project_ids = {proj.id for proj in current_projects}
            anticipated_project_ids = {proj.id for proj in anticipated_projects}

            # Process each week
            for i in range(num_weeks):
                week_start = week_dates[i]
                week_end = week_start + timedelta(days=6)

                # Carry forward previous week's balance (except for first week)
                if i > 0:
                    working_capital[i] = working_capital[i - 1]
                    savings_balance[i] = savings_balance[i - 1]
                    accounts_totals[i] = accounts_totals[i - 1]

                    # Carry forward expense totals arrays
                    payroll_expenses_totals[i] = payroll_expenses_totals[i - 1]
                    fixed_expenses_totals[i] = fixed_expenses_totals[i - 1]
                    misc_expenses_totals[i] = misc_expenses_totals[i - 1]

                    # Carry forward project cash flow arrays
                    current_projects_expenses[i] = current_projects_expenses[i - 1]
                    current_projects_purchase_orders[i] = (
                        current_projects_purchase_orders[i - 1]
                    )
                    current_projects_invoices[i] = current_projects_invoices[i - 1]
                    anticipated_projects_expenses[i] = anticipated_projects_expenses[
                        i - 1
                    ]
                    anticipated_projects_purchase_orders[i] = (
                        anticipated_projects_purchase_orders[i - 1]
                    )
                    anticipated_projects_invoices[i] = anticipated_projects_invoices[
                        i - 1
                    ]

                    # Carry forward project working capital arrays
                    current_projects_working_capital[i] = (
                        current_projects_working_capital[i - 1]
                    )
                    anticipated_projects_working_capital[i] = (
                        anticipated_projects_working_capital[i - 1]
                    )

                # Process fixed expenses
                for expense in fixed_expenses:
                    if self._should_apply_recurring_expense(
                        expense, week_start, week_end
                    ):
                        amount = float(expense.amount)
                        working_capital[i] -= amount
                        accounts_totals[i] -= amount
                        fixed_expenses_totals[i] += amount
                        fixed_expenses_weekly[i] += amount

                        # Apply to project cash flow arrays
                        current_projects_expenses[i] -= amount
                        current_projects_purchase_orders[i] -= amount
                        current_projects_invoices[i] -= amount
                        anticipated_projects_expenses[i] -= amount
                        anticipated_projects_purchase_orders[i] -= amount
                        anticipated_projects_invoices[i] -= amount

                # Process misc expenses
                for expense in misc_expenses:
                    expense_date = to_date_only(expense.date)
                    if expense_date >= week_start and expense_date <= week_end:
                        amount = float(expense.amount)
                        working_capital[i] -= amount
                        accounts_totals[i] -= amount
                        misc_expenses_totals[i] += amount
                        misc_expenses_weekly[i] += amount

                        # Apply to project cash flow arrays
                        current_projects_expenses[i] -= amount
                        current_projects_purchase_orders[i] -= amount
                        current_projects_invoices[i] -= amount
                        anticipated_projects_expenses[i] -= amount
                        anticipated_projects_purchase_orders[i] -= amount
                        anticipated_projects_invoices[i] -= amount

                # Process payroll expenses
                for expense in payroll_expenses:
                    if self._should_apply_recurring_expense(
                        expense, week_start, week_end
                    ):
                        # Check if this is the first occurrence for this expense
                        expense_date = to_date_only(expense.date)
                        is_first_occurrence = False

                        # Determine if this is the first payroll occurrence after the expense date
                        if expense.recurrence and expense.recurrence.lower() in [
                            "weekly",
                            "bi-weekly",
                            "biweekly",
                            "monthly",
                            "1st & 3rd fridays",
                        ]:
                            # Check if this is the first week where this expense applies
                            if (
                                hasattr(expense, "week_of_month")
                                and hasattr(expense, "day_of_week")
                                and expense.week_of_month is not None
                                and expense.day_of_week is not None
                            ):
                                # For expenses with specific scheduling, check if this is the first occurrence
                                if expense.recurrence.lower() == "monthly":
                                    first_payroll_date = self._get_nth_weekday_of_month(
                                        expense_date.year,
                                        expense_date.month,
                                        expense.week_of_month,
                                        expense.day_of_week,
                                    )
                                    if (
                                        first_payroll_date
                                        and first_payroll_date < expense_date
                                    ):
                                        # Move to next month
                                        next_month = expense_date.month + 1
                                        next_year = expense_date.year
                                        if next_month > 12:
                                            next_month = 1
                                            next_year += 1
                                        first_payroll_date = (
                                            self._get_nth_weekday_of_month(
                                                next_year,
                                                next_month,
                                                expense.week_of_month,
                                                expense.day_of_week,
                                            )
                                        )
                                    is_first_occurrence = (
                                        first_payroll_date
                                        and week_start <= first_payroll_date <= week_end
                                    )
                                elif expense.recurrence.lower() in [
                                    "bi-weekly",
                                    "biweekly",
                                ]:
                                    # Similar logic for biweekly
                                    first_payroll_date = self._get_nth_weekday_of_month(
                                        expense_date.year,
                                        expense_date.month,
                                        expense.week_of_month,
                                        expense.day_of_week,
                                    )
                                    if (
                                        first_payroll_date
                                        and first_payroll_date < expense_date
                                    ):
                                        next_month = expense_date.month + 1
                                        next_year = expense_date.year
                                        if next_month > 12:
                                            next_month = 1
                                            next_year += 1
                                        first_payroll_date = (
                                            self._get_nth_weekday_of_month(
                                                next_year,
                                                next_month,
                                                expense.week_of_month,
                                                expense.day_of_week,
                                            )
                                        )
                                    is_first_occurrence = (
                                        first_payroll_date
                                        and week_start <= first_payroll_date <= week_end
                                    )
                            elif expense.recurrence.lower() == "1st & 3rd fridays":
                                # For 1st & 3rd Fridays, find the first occurrence
                                current_month = expense_date.month
                                current_year = expense_date.year

                                # Get all Fridays in current month
                                fridays = self._get_fridays_of_month(
                                    current_year, current_month
                                )

                                # Find the first occurrence on or after expense date
                                first_payroll_date = None
                                if len(fridays) > 0 and fridays[0] >= expense_date:
                                    first_payroll_date = fridays[0]
                                elif len(fridays) > 2 and fridays[2] >= expense_date:
                                    first_payroll_date = fridays[2]
                                else:
                                    # Move to next month's 1st Friday
                                    next_month = current_month + 1
                                    next_year = current_year
                                    if next_month > 12:
                                        next_month = 1
                                        next_year += 1

                                    next_fridays = self._get_fridays_of_month(
                                        next_year, next_month
                                    )
                                    if len(next_fridays) > 0:
                                        first_payroll_date = next_fridays[0]

                                is_first_occurrence = (
                                    first_payroll_date
                                    and week_start <= first_payroll_date <= week_end
                                )
                            else:
                                # For expenses without specific scheduling, use simpler logic
                                is_first_occurrence = (
                                    week_start <= expense_date <= week_end
                                )

                        # Calculate the payroll amount (potentially prorated for first occurrence)
                        amount = self._calculate_payroll_amount(
                            expense, week_start, week_end, is_first_occurrence
                        )

                        working_capital[i] -= amount
                        accounts_totals[i] -= amount
                        payroll_expenses_totals[i] += amount
                        payroll_expenses_weekly[i] += amount

                        # Apply to project cash flow arrays
                        current_projects_expenses[i] -= amount
                        current_projects_purchase_orders[i] -= amount
                        current_projects_invoices[i] -= amount
                        anticipated_projects_expenses[i] -= amount
                        anticipated_projects_purchase_orders[i] -= amount
                        anticipated_projects_invoices[i] -= amount

                # Process current projects
                for project in current_projects:
                    # Process milestones - add to all project arrays (income)
                    for milestone in project.milestones:
                        milestone_date = to_date_only(milestone.date)
                        if milestone_date >= week_start and milestone_date <= week_end:
                            amount = float(milestone.amount)
                            savings_amount = amount * (project.savings_percentage / 100)
                            savings_balance[i] += savings_amount

                            # Add milestone income to accounts totals and working capital
                            accounts_totals[i] += amount - savings_amount
                            working_capital[i] += amount - savings_amount

                            # Add milestone income to all project arrays
                            current_projects_expenses[i] += amount
                            current_projects_purchase_orders[i] += amount
                            current_projects_invoices[i] += amount

                            # Apply savings to project arrays
                            current_projects_expenses[i] -= savings_amount
                            current_projects_purchase_orders[i] -= savings_amount
                            current_projects_invoices[i] -= savings_amount

                            # Add milestone income to current projects working capital
                            current_projects_working_capital[i] += (
                                amount - savings_amount
                            )

                    # Process project expenses - apply to expenses and subsequent arrays
                    for cost in project.project_expenses:
                        cost_date = to_date_only(cost.date)
                        if cost_date >= week_start and cost_date <= week_end:
                            amount = float(cost.amount)

                            # Apply project expenses to accounts totals and working capital
                            # accounts_totals[i] -= amount
                            # working_capital[i] -= amount

                            # Apply project expenses to appropriate arrays
                            current_projects_expenses[i] -= amount

                # Process anticipated projects
                for project in anticipated_projects:
                    # Process milestones - add to anticipated project arrays only
                    for milestone in project.milestones:
                        milestone_date = to_date_only(milestone.date)
                        if milestone_date >= week_start and milestone_date <= week_end:
                            amount = float(milestone.amount)
                            savings_amount = amount * (project.savings_percentage / 100)
                            savings_balance[i] += savings_amount

                            # Add milestone income to accounts totals and working capital
                            accounts_totals[i] += amount - savings_amount
                            working_capital[i] += amount - savings_amount

                            # Add milestone income to anticipated project arrays only
                            anticipated_projects_expenses[i] += amount
                            anticipated_projects_purchase_orders[i] += amount
                            anticipated_projects_invoices[i] += amount

                            # Apply savings to anticipated project arrays
                            anticipated_projects_expenses[i] -= savings_amount
                            anticipated_projects_purchase_orders[i] -= savings_amount
                            anticipated_projects_invoices[i] -= savings_amount

                            # Add milestone income to anticipated projects working capital
                            anticipated_projects_working_capital[i] += (
                                amount - savings_amount
                            )

                    # Process project expenses - apply to anticipated arrays only
                    for cost in project.project_expenses:
                        cost_date = to_date_only(cost.date)
                        if cost_date >= week_start and cost_date <= week_end:
                            amount = float(cost.amount)

                            # Apply project expenses to accounts totals and working capital
                            # accounts_totals[i] -= amount
                            # working_capital[i] -= amount

                            # Apply project expenses to anticipated arrays only
                            anticipated_projects_expenses[i] -= amount

                # Process purchase orders
                for po in purchase_orders:
                    # Use stored due_date
                    if po.due_date:
                        payment_date = to_date_only(po.due_date)
                        if payment_date >= week_start and payment_date <= week_end:
                            amount = float(po.amount)

                            # Apply purchase order to accounts totals and working capital
                            # accounts_totals[i] -= amount
                            # working_capital[i] -= amount

                            if po.project_id in current_project_ids:
                                # Apply to current project purchase order and invoice arrays
                                current_projects_purchase_orders[i] -= amount

                            elif po.project_id in anticipated_project_ids:
                                # Apply to anticipated project purchase order and invoice arrays
                                anticipated_projects_purchase_orders[i] -= amount

                # Process invoices
                for invoice in invoices:
                    invoice_date = to_date_only(invoice.due_date)
                    if invoice_date >= week_start and invoice_date <= week_end:
                        amount = float(invoice.amount)

                        # Apply invoice income to accounts totals and working capital
                        accounts_totals[i] -= amount
                        working_capital[i] -= amount

                        if invoice.project_id in current_project_ids:
                            # Apply to current project invoice array only
                            current_projects_invoices[i] -= amount
                            current_projects_working_capital[i] -= amount
                        elif invoice.project_id in anticipated_project_ids:
                            # Apply to anticipated project invoice array only
                            anticipated_projects_invoices[i] -= amount
                            anticipated_projects_working_capital[i] -= amount

            # Convert project data to response format
            current_projects_response = [
                create_project_response(project) for project in current_projects
            ]
            anticipated_projects_response = {
                "projects": [
                    create_project_response(project) for project in anticipated_projects
                ],
                "archived": [],  # We don't need archived projects for forecast calculations
            }

            # Convert accounts to response format
            from app.cash_flow.models import AccountResponse

            accounts_response = [
                AccountResponse.model_validate(account) for account in accounts
            ]

            return ForecastResponse(
                week_dates=[d.strftime("%Y-%m-%d") for d in week_dates],
                working_capital=working_capital,
                savings_balance=savings_balance,
                accounts_totals=accounts_totals,
                payroll_expenses_totals=payroll_expenses_totals,
                payroll_expenses_weekly=payroll_expenses_weekly,
                fixed_expenses_totals=fixed_expenses_totals,
                fixed_expenses_weekly=fixed_expenses_weekly,
                misc_expenses_totals=misc_expenses_totals,
                misc_expenses_weekly=misc_expenses_weekly,
                current_projects_expenses=current_projects_expenses,
                current_projects_purchase_orders=current_projects_purchase_orders,
                current_projects_invoices=current_projects_invoices,
                anticipated_projects_expenses=anticipated_projects_expenses,
                anticipated_projects_purchase_orders=anticipated_projects_purchase_orders,
                anticipated_projects_invoices=anticipated_projects_invoices,
                current_projects_working_capital=current_projects_working_capital,
                anticipated_projects_working_capital=anticipated_projects_working_capital,
                current_projects=current_projects_response,
                anticipated_projects=anticipated_projects_response,
                accounts=accounts_response,
            )
        except Exception as e:
            raise ValueError(f"Failed to generate forecast: {str(e)}")
