import json
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.db_operations import SnapshotOperations
from app.cash_flow.services import ForecastService
from app.cash_flow.models import (
    SnapshotTable,
    SnapshotUpsert,
    SnapshotResponse,
    DeleteRequest,
)


class SnapshotService:
    def __init__(self, session: AsyncSession):
        self.db = SnapshotOperations(session)
        self.forecast_service = ForecastService(session)

    async def get_snapshots(self, organization_id: int) -> List[SnapshotResponse]:
        """Get forecast snapshots."""
        snapshots = await self.db.get_snapshots(organization_id)
        return [SnapshotResponse.model_validate(snapshot) for snapshot in snapshots]

    async def save_snapshot(
        self, snapshot_create: SnapshotUpsert, organization_id: int
    ) -> SnapshotResponse:
        """Save a forecast snapshot."""
        try:
            # The dates are already parsed by the pydantic model validator
            start_date = snapshot_create.start_date
            end_date = snapshot_create.end_date

            forecast_response = await self.forecast_service.generate_forecast(
                start_date,
                end_date,
                organization_id,
            )

            # Create a new snapshot
            db_snapshot = SnapshotTable(
                name=snapshot_create.name,
                start_date=start_date,
                end_date=end_date,
                week_dates=json.dumps(forecast_response.week_dates),
                working_capital=json.dumps(forecast_response.working_capital),
                savings_balance=json.dumps(forecast_response.savings_balance),
                current_projects_expenses=json.dumps(
                    forecast_response.current_projects_expenses
                ),
                current_projects_purchase_orders=json.dumps(
                    forecast_response.current_projects_purchase_orders
                ),
                current_projects_invoices=json.dumps(
                    forecast_response.current_projects_invoices
                ),
                anticipated_projects_expenses=json.dumps(
                    forecast_response.anticipated_projects_expenses
                ),
                anticipated_projects_purchase_orders=json.dumps(
                    forecast_response.anticipated_projects_purchase_orders
                ),
                anticipated_projects_invoices=json.dumps(
                    forecast_response.anticipated_projects_invoices
                ),
                current_projects_working_capital=json.dumps(
                    forecast_response.current_projects_working_capital
                ),
                anticipated_projects_working_capital=json.dumps(
                    forecast_response.anticipated_projects_working_capital
                ),
                organization_id=organization_id,
            )

            # Save to database
            saved_snapshot = await self.db.save_snapshot(db_snapshot)

            return SnapshotResponse.model_validate(saved_snapshot)

        except Exception as e:
            raise ValueError(f"Failed to save snapshot: {str(e)}")

    async def get_snapshot(self, id: int, organization_id: int) -> SnapshotResponse:
        """Get a specific snapshot by ID."""
        snapshot = await self.db.get_snapshot_by_id(id, organization_id)
        if not snapshot:
            raise ValueError(f"Snapshot with ID {id} not found")
        return SnapshotResponse.model_validate(snapshot)

    async def delete_snapshot(
        self, delete_req: DeleteRequest, organization_id: int
    ) -> None:
        """Delete a snapshot."""
        deleted = await self.db.delete_snapshot(delete_req.id, organization_id)
        if not deleted:
            raise ValueError(f"Snapshot with ID {delete_req.id} not found")
