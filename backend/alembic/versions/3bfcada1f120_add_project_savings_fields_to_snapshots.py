"""Add project savings fields to snapshots

Revision ID: 3bfcada1f120
Revises: e54ab2e1ffc1
Create Date: 2025-07-25 17:35:01.589769

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '3bfcada1f120'
down_revision: Union[str, None] = 'e54ab2e1ffc1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('snapshots', sa.Column('current_projects_savings', sa.String(), nullable=True))
    op.add_column('snapshots', sa.Column('anticipated_projects_savings', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('snapshots', 'anticipated_projects_savings')
    op.drop_column('snapshots', 'current_projects_savings')
    # ### end Alembic commands ### 