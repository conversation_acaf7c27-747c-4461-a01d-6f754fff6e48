"""Add project working capital fields to snapshots

Revision ID: e54ab2e1ffc1
Revises: 302bd981ea00
Create Date: 2025-07-25 13:17:16.140897

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = 'e54ab2e1ffc1'
down_revision: Union[str, None] = '302bd981ea00'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('snapshots', sa.Column('current_projects_working_capital', sa.String(), nullable=True))
    op.add_column('snapshots', sa.Column('anticipated_projects_working_capital', sa.String(), nullable=True))
    op.alter_column('tag_mappings', 'tag_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('tag_mappings', 'tag_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.drop_column('snapshots', 'anticipated_projects_working_capital')
    op.drop_column('snapshots', 'current_projects_working_capital')
    # ### end Alembic commands ### 