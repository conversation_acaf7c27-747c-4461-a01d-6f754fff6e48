"""Add project savings fields to snapshots

Revision ID: add_project_savings_fields
Revises: e54ab2e1ffc1
Create Date: 2025-07-25 14:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_project_savings_fields'
down_revision: Union[str, None] = 'e54ab2e1ffc1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('snapshots', sa.Column('current_projects_savings', sa.String(), nullable=True))
    op.add_column('snapshots', sa.Column('anticipated_projects_savings', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('snapshots', 'anticipated_projects_savings')
    op.drop_column('snapshots', 'current_projects_savings')
    # ### end Alembic commands ###
