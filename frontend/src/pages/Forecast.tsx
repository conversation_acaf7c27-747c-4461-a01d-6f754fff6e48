import { Tag as AntdTag, Card, Col, message, Row, Typography } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import React, { useEffect, useRef, useState } from 'react';
import AccountBalances from '../components/AccountBalances';
import ForecastChart from '../components/ForecastChart';
import ForecastForm from '../components/ForecastForm';
import OverheadTable from '../components/OverheadTable';
import ProjectTable from '../components/ProjectTable';
import SnapshotsPanel from '../components/SnapshotsPanel';
import { useOrganization } from '../contexts/OrganizationContext';
import {
  addInvoice,
  addPurchaseOrder,
  createMilestone,
  createProjectExpense,
  createTag,
  deleteForecastSnapshot,
  deleteInvoice,
  deleteMilestone,
  deleteProjectExpense,
  deletePurchaseOrder,
  generateForecast,
  getForecastSnapshots,
  getTags,
  saveForecastSnapshot,
  updateInvoice,
  updateMilestone,
  updateProjectExpense,
  updatePurchaseOrder,
} from '../services/api';
import {
  Account,
  ChartData,
  ForecastSnapshot,
  LineVisibility,
  OverheadData,
  Project,
  ProjectsByWeek,
  Tag,
  WeeklyItem,
} from '../types';
import { calculateIssueDate } from '../utils/helpers';

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);

const Forecast: React.FC = () => {
  const [chartData, setChartData] = useState<ChartData<'line'> | null>(null);
  const [currentForecastLabels, setcurrentForecastLabels] = useState<string[]>([]);
  const [overheadData, setOverheadData] = useState<OverheadData | null>(null);
  const [snapshots, setSnapshots] = useState<ForecastSnapshot[]>([]);
  const [snapshotData, setSnapshotData] = useState<Record<string | number, ForecastSnapshot>>({});
  const [visibleLines, setVisibleLines] = useState<Record<string | number, boolean>>({});
  const visibleLinesRef = useRef(visibleLines);
  const currentForecastData = useRef<any>(null);
  const [lineVisibility, setLineVisibility] = useState<LineVisibility>(() => {
    // Load saved visibility state from localStorage
    const savedVisibility = localStorage.getItem('forecastLineVisibility');
    if (savedVisibility) {
      try {
        return JSON.parse(savedVisibility);
      } catch (e) {
        console.error('Failed to parse saved visibility state:', e);
      }
    }
    // Default values if no saved state exists
    return {
      working_capital: true,
      savings_cash: true,
      current_projects_expenses: false,
      current_projects_purchase_orders: false,
      current_projects_invoices: false,
      anticipated_projects_expenses: false,
      anticipated_projects_purchase_orders: false,
      anticipated_projects_invoices: false,
      current_projects_working_capital: true,
      anticipated_projects_working_capital: true,
    };
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [savingSnapshot, setSavingSnapshot] = useState<boolean>(false);
  const [projects, setProjects] = useState<Record<string, Project[]>>({
    current: [],
    anticipated: [],
  });
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [projectsByWeek, setProjectsByWeek] = useState<Record<string, ProjectsByWeek>>({
    current: {},
    anticipated: {},
  });
  const [weeklyTotals, setWeeklyTotals] = useState<Record<string, Record<string, number>>>({
    current: {},
    anticipated: {},
  });
  const [runningTotals, setRunningTotals] = useState<Record<string, Record<string, number>>>({
    current: {},
    anticipated: {},
  });
  const [currentDateRange, setCurrentDateRange] = useState<{
    startWeek: Dayjs | null;
    endWeek: Dayjs | null;
  }>({
    startWeek: null,
    endWeek: null,
  });
  const { organization } = useOrganization();
  const [allTags, setAllTags] = useState<Tag[]>([]);
  const [tagOptions, setTagOptions] = useState<{ label: string; value: string }[]>([]);
  const weekDatesRef = useRef<string[]>([]);
  // Add new state for last submitted values
  const [lastSubmittedValues, setLastSubmittedValues] = useState<{
    startWeek: Dayjs;
    endWeek: Dayjs;
  } | null>(() => {
    const saved = localStorage.getItem('lastForecastDateRange');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        return {
          startWeek: dayjs(parsed.startWeek),
          endWeek: dayjs(parsed.endWeek),
        };
      } catch (e) {
        console.error('Failed to parse saved date range:', e);
        return null;
      }
    }
    return null;
  });

  // Save visibility state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('forecastLineVisibility', JSON.stringify(lineVisibility));
  }, [lineVisibility]);

  useEffect(() => {
    visibleLinesRef.current = visibleLines;
  }, [visibleLines]);

  useEffect(() => {
    console.log('fetching forecast');
    fetchForecastSnapshots();
    fetchAllTags();
  }, []);

  // Save last submitted values to localStorage whenever they change
  useEffect(() => {
    if (lastSubmittedValues) {
      localStorage.setItem(
        'lastForecastDateRange',
        JSON.stringify({
          startWeek: lastSubmittedValues.startWeek.toISOString(),
          endWeek: lastSubmittedValues.endWeek.toISOString(),
        })
      );
    }
  }, [lastSubmittedValues]);

  // Generate forecast on component mount if we have last submitted values
  useEffect(() => {
    if (lastSubmittedValues) {
      handleSubmit(lastSubmittedValues);
    }
  }, []);

  // Refresh category tags for existing items when allTags changes
  useEffect(() => {
    if (allTags.length > 0) {
      setProjectsByWeek(prev => {
        const newProjectsByWeek = { ...prev };

        // Update both current and anticipated projects
        ['current', 'anticipated'].forEach(type => {
          Object.values(newProjectsByWeek[type]).forEach(projectData => {
            Object.values(projectData.weeklyData).forEach((weekData: any) => {
              weekData.items.forEach((item: any) => {
                // Update category_tag if the item has a category_tag_id
                if (item.category_tag_id) {
                  const categoryTag = allTags.find(tag => tag.id === item.category_tag_id);
                  if (categoryTag) {
                    item.category_tag = categoryTag;
                  }
                }
              });
            });
          });
        });

        return newProjectsByWeek;
      });
    }
  }, [allTags]);

  const fetchAllTags = async () => {
    try {
      const response = await getTags();
      const sortedTags = response.data.sort((a: Tag, b: Tag) => a.name.localeCompare(b.name));
      setAllTags(sortedTags);
      setTagOptions(
        sortedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );
    } catch (error: any) {
      console.error('Error fetching tags:', error);
      message.error('Failed to load tags');
    }
  };

  const fetchForecastSnapshots = async (): Promise<void> => {
    setLoading(true);
    try {
      console.log('Inside fetch forecast');
      const { data } = await getForecastSnapshots();
      const snapshotsData: ForecastSnapshot[] = data;

      const formattedSnapshots: ForecastSnapshot[] = snapshotsData.map((s: ForecastSnapshot) => s);
      console.log('formattedSnapshots:', formattedSnapshots);
      setSnapshots(formattedSnapshots);
      setSnapshotData(
        formattedSnapshots.reduce(
          (acc, s) => ({ ...acc, [s.id]: s }),
          {} as Record<string | number, ForecastSnapshot>
        )
      );
      setVisibleLines(
        formattedSnapshots.reduce(
          (acc, s) => ({ ...acc, [s.id]: visibleLines[s.id] ?? false }),
          {} as Record<string | number, boolean>
        )
      );
    } catch (error: any) {
      console.error('Error in fetchForecastSnapshots:', error);
      console.error('Error stack:', error.stack);
      message.error('Failed to load forecast data: ' + (error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const calculateProjectData = (
    projects: Project[]
  ): [ProjectsByWeek, Record<string, number>, Record<string, number>] => {
    const projectsData: ProjectsByWeek = {};
    const totals: Record<string, number> = {};
    const runningTotals: Record<string, number> = {};

    weekDatesRef.current.forEach(weekDate => {
      totals[weekDate] = 0;
      runningTotals[weekDate] = 0;
    });

    projects.forEach(project => {
      projectsData[project.id] = {
        project,
        weeklyData: {},
      };

      weekDatesRef.current.forEach(weekDate => {
        projectsData[project.id].weeklyData[weekDate] = { total: 0, items: [] };
      });

      // Helper function to ensure category_tag object is set
      const ensureCategoryTag = (item: any) => {
        if (item.category_tag_id && !item.category_tag) {
          const categoryTag = allTags.find(tag => tag.id === item.category_tag_id);
          if (categoryTag) {
            item.category_tag = categoryTag;
          }
        }
        return item;
      };

      // Process milestones (positive cash flow)
      project.milestones.forEach(milestone => {
        const weekKey = findWeekKey(milestone.date);
        if (weekKey) {
          projectsData[project.id].weeklyData[weekKey].items.push(
            ensureCategoryTag({
              ...milestone,
              type: 'milestone',
              displayDescription: processItemDescription(
                { ...milestone, type: 'milestone' },
                project
              ),
              displayTitle: processItemTitle({ ...milestone, type: 'milestone' }),
            } as const)
          );
          projectsData[project.id].weeklyData[weekKey].total += Number(milestone.amount);
          totals[weekKey] += Number(milestone.amount);
        }
      });

      // Process project expenses (expected spending by category) - show but don't affect totals
      project.project_expenses.forEach(expense => {
        const weekKey = findWeekKey(expense.date);
        if (weekKey) {
          const itemWithType = {
            ...expense,
            type: 'projectExpense',
          };

          projectsData[project.id].weeklyData[weekKey].items.push(
            ensureCategoryTag({
              ...itemWithType,
              displayDescription: processItemDescription(itemWithType, project),
              displayTitle: processItemTitle(itemWithType),
            } as const)
          );
        }
      });

      // Process purchase orders (firm projections of spending) - show but don't affect totals
      project.purchase_orders.forEach(po => {
        const weekKey = findWeekKey(po.due_date);
        if (weekKey) {
          const itemWithType = {
            ...po,
            type: 'purchaseOrder',
            date: po.due_date,
          };

          projectsData[project.id].weeklyData[weekKey].items.push(
            ensureCategoryTag({
              ...itemWithType,
              displayDescription: processItemDescription(itemWithType, project),
              displayTitle: processItemTitle(itemWithType),
            })
          );
        }
      });

      // Process invoices (actual debts that will leave the account)
      project.invoices.forEach(invoice => {
        const weekKey = findWeekKey(invoice.due_date);
        if (weekKey) {
          const itemWithType = {
            ...invoice,
            type: 'invoice',
            date: invoice.due_date,
          };

          projectsData[project.id].weeklyData[weekKey].items.push(
            ensureCategoryTag({
              ...itemWithType,
              displayDescription: processItemDescription(itemWithType, project),
              displayTitle: processItemTitle(itemWithType),
            })
          );
          projectsData[project.id].weeklyData[weekKey].total -= Number(invoice.amount);
          totals[weekKey] -= Number(invoice.amount);
        }
      });
    });

    let cumulativeTotal = 0;
    weekDatesRef.current.forEach(weekDate => {
      cumulativeTotal += totals[weekDate];
      runningTotals[weekDate] = cumulativeTotal;
    });

    return [projectsData, totals, runningTotals];
  };

  const findWeekKey = (itemDate: Dayjs | string): string | undefined => {
    if (typeof itemDate === 'string') {
      itemDate = dayjs(itemDate);
    }
    const date = dayjs(itemDate);
    return weekDatesRef.current.find(weekDate => {
      const weekStart = dayjs(weekDate);
      const weekEnd = weekStart.day(6);
      return date.isSameOrAfter(weekStart) && date.isSameOrBefore(weekEnd);
    });
  };

  // Helper function to process item title
  const processItemTitle = (item: any): string => {
    let title = '';

    if (item.type === 'projectExpense') {
      title = item.description;
    } else if (item.type === 'purchaseOrder') {
      title = `PO# ${item.po_number}`;
    } else if (item.type === 'invoice') {
      title = `Invoice # ${item.invoice_number}`;
    } else if (item.type === 'milestone') {
      title = item.description;
    }

    return title;
  };

  // Helper function to process item descriptions to show relationships
  const processItemDescription = (item: any, project: Project): string => {
    let description = '';

    if (item.type === 'projectExpense') {
      // Find all purchase orders and invoices related to this project expense
      const relatedPOs = project.purchase_orders.filter(
        po => po.category_tag?.id && po.category_tag?.id === item.category_tag?.id
      );
      const relatedInvoices = project.invoices.filter(
        inv => inv.category_tag?.id && inv.category_tag?.id === item.category_tag?.id
      );

      // Calculate the remaining amount after accounting for POs and invoices
      const poAmount = relatedPOs.reduce((sum, po) => sum + Number(po.amount), 0);
      const invoiceAmount = relatedInvoices.reduce((sum, inv) => sum + Number(inv.amount), 0);
      const remainingAmount = Number(item.amount) - poAmount - invoiceAmount;

      // Only show the remaining amount if there are no related POs or if there's still money left
      if (relatedPOs.length > 0) {
        const poNumbers = relatedPOs.map(po => po.po_number).join(', ');
        description += ` (POs: ${poNumbers})`;
      }
      if (relatedInvoices.length > 0) {
        const invoiceNumbers = relatedInvoices.map(inv => inv.id).join(', ');
        description += ` (Invoices: ${invoiceNumbers})`;
      }

      description += ` (Remaining: $${remainingAmount.toLocaleString()})`;
    } else if (item.type === 'purchaseOrder') {
      // Find related invoices for this PO
      const relatedInvoices = project.invoices.filter(inv => inv.purchase_order_id === item.id);
      const invoiceAmount = relatedInvoices.reduce((sum, inv) => sum + Number(inv.amount), 0);
      const remainingAmount = Number(item.amount) - invoiceAmount;

      // Find related project expense
      const relatedExpense = item.category_tag?.id
        ? project.project_expenses.find(exp => exp.category_tag?.id === item.category_tag?.id)
        : null;

      if (relatedExpense) {
        description += ` (PE: ${relatedExpense.description})`;
      }
      if (relatedInvoices.length > 0) {
        description += ` (Invoices: $${invoiceAmount.toLocaleString()})`;
      }
      description += ` (Remaining: $${remainingAmount.toLocaleString()})`;
    } else if (item.type === 'invoice') {
      // Find the related project expense if this invoice is linked to a PO that's linked to a project expense
      if (item.category_tag) {
        const relatedExpense = project.project_expenses.find(
          exp => exp.category_tag?.id === item.category_tag?.id
        );
        if (relatedExpense) {
          description += ` (PE: ${relatedExpense.description} - $${relatedExpense.amount})`;
        }
      } else {
        const relatedPO = item.purchase_order_id
          ? project.purchase_orders.find(po => po.id === item.purchase_order_id)
          : null;
        if (relatedPO) {
          description += ` (PO: ${relatedPO.po_number} - $${relatedPO.amount})`;
        }
      }
    }

    return description;
  };

  // Helper function to create snapshot datasets
  const createSnapshotDatasets = (
    snapshot: ForecastSnapshot,
    index: number,
    lineVisibility: LineVisibility
  ) => {
    const snapshotName = snapshot.name || snapshot.id;

    // Define color families for snapshots that maintain visual grouping
    const snapshotColorFamilies = [
      {
        // Purple family
        working_capital: 'rgba(138, 43, 226, 0.8)',
        savings: 'rgba(70, 130, 180, 0.8)',
        currentProjects: {
          expenses: 'rgba(128, 0, 128, 0.8)',
          purchaseOrders: 'rgba(186, 85, 211, 0.8)',
          invoices: 'rgba(221, 160, 221, 0.8)',
          workingCapital: 'rgba(148, 0, 211, 0.8)',
        },
        anticipatedProjects: {
          expenses: 'rgba(75, 0, 130, 0.8)',
          purchaseOrders: 'rgba(123, 104, 238, 0.8)',
          invoices: 'rgba(176, 196, 222, 0.8)',
          workingCapital: 'rgba(106, 90, 205, 0.8)',
        },
      },
      {
        // Orange family
        working_capital: 'rgba(255, 140, 0, 0.8)',
        savings: 'rgba(65, 105, 225, 0.8)',
        currentProjects: {
          expenses: 'rgba(255, 69, 0, 0.8)',
          purchaseOrders: 'rgba(255, 99, 71, 0.8)',
          invoices: 'rgba(255, 160, 122, 0.8)',
          workingCapital: 'rgba(255, 20, 147, 0.8)',
        },
        anticipatedProjects: {
          expenses: 'rgba(205, 92, 92, 0.8)',
          purchaseOrders: 'rgba(233, 150, 122, 0.8)',
          invoices: 'rgba(250, 128, 114, 0.8)',
          workingCapital: 'rgba(219, 112, 147, 0.8)',
        },
      },
      {
        // Teal family
        working_capital: 'rgba(0, 128, 128, 0.8)',
        savings: 'rgba(100, 149, 237, 0.8)',
        currentProjects: {
          expenses: 'rgba(0, 139, 139, 0.8)',
          purchaseOrders: 'rgba(72, 209, 204, 0.8)',
          invoices: 'rgba(175, 238, 238, 0.8)',
          workingCapital: 'rgba(32, 178, 170, 0.8)',
        },
        anticipatedProjects: {
          expenses: 'rgba(95, 158, 160, 0.8)',
          purchaseOrders: 'rgba(176, 224, 230, 0.8)',
          invoices: 'rgba(224, 255, 255, 0.8)',
          workingCapital: 'rgba(127, 255, 212, 0.8)',
        },
      },
      {
        // Brown family
        working_capital: 'rgba(139, 69, 19, 0.8)',
        savings: 'rgba(72, 61, 139, 0.8)',
        currentProjects: {
          expenses: 'rgba(165, 42, 42, 0.8)',
          purchaseOrders: 'rgba(205, 133, 63, 0.8)',
          invoices: 'rgba(222, 184, 135, 0.8)',
          workingCapital: 'rgba(160, 82, 45, 0.8)',
        },
        anticipatedProjects: {
          expenses: 'rgba(188, 143, 143, 0.8)',
          purchaseOrders: 'rgba(210, 180, 140, 0.8)',
          invoices: 'rgba(245, 222, 179, 0.8)',
          workingCapital: 'rgba(218, 165, 32, 0.8)',
        },
      },
    ];

    const colorFamily = snapshotColorFamilies[index % snapshotColorFamilies.length];

    return [
      {
        label: `Working Capital (${snapshotName})`,
        data: snapshot.working_capital || [],
        lineColor: colorFamily.working_capital,
        borderWidth: 2,
        fill: false,
        hidden: !lineVisibility.working_capital,
      },
      {
        label: `Savings (${snapshotName})`,
        data: snapshot.savings_balance || [],
        lineColor: colorFamily.savings,
        fill: false,
        hidden: !lineVisibility.savings_cash,
      },
      {
        label: `Current Projects Expenses (${snapshotName})`,
        data: snapshot.current_projects_expenses || [],
        lineColor: colorFamily.currentProjects.expenses,
        fill: false,
        hidden: !lineVisibility.current_projects_expenses,
      },
      {
        label: `Current Projects Purchase Orders (${snapshotName})`,
        data: snapshot.current_projects_purchase_orders || [],
        lineColor: colorFamily.currentProjects.purchaseOrders,
        fill: false,
        hidden: !lineVisibility.current_projects_purchase_orders,
      },
      {
        label: `Current Projects Invoices (${snapshotName})`,
        data: snapshot.current_projects_invoices || [],
        lineColor: colorFamily.currentProjects.invoices,
        fill: false,
        hidden: !lineVisibility.current_projects_invoices,
      },
      {
        label: `Anticipated Projects Expenses (${snapshotName})`,
        data: snapshot.anticipated_projects_expenses || [],
        lineColor: colorFamily.anticipatedProjects.expenses,
        fill: false,
        hidden: !lineVisibility.anticipated_projects_expenses,
      },
      {
        label: `Anticipated Projects Purchase Orders (${snapshotName})`,
        data: snapshot.anticipated_projects_purchase_orders || [],
        lineColor: colorFamily.anticipatedProjects.purchaseOrders,
        fill: false,
        hidden: !lineVisibility.anticipated_projects_purchase_orders,
      },
      {
        label: `Anticipated Projects Invoices (${snapshotName})`,
        data: snapshot.anticipated_projects_invoices || [],
        lineColor: colorFamily.anticipatedProjects.invoices,
        fill: false,
        hidden: !lineVisibility.anticipated_projects_invoices,
      },
      {
        label: `Current Projects Working Capital (${snapshotName})`,
        data: snapshot.current_projects_working_capital || [],
        lineColor: colorFamily.currentProjects.workingCapital,
        fill: false,
        hidden: !lineVisibility.current_projects_working_capital,
      },
      {
        label: `Anticipated Projects Working Capital (${snapshotName})`,
        data: snapshot.anticipated_projects_working_capital || [],
        lineColor: colorFamily.anticipatedProjects.workingCapital,
        fill: false,
        hidden: !lineVisibility.anticipated_projects_working_capital,
      },
    ];
  };

  const handleSubmit = async (values: { startWeek: Dayjs; endWeek: Dayjs }): Promise<void> => {
    setSubmitting(true);
    try {
      setCurrentDateRange(values);
      setLastSubmittedValues(values); // Store the submitted values
      const { data } = await generateForecast({
        start_date: values.startWeek,
        end_date: values.endWeek,
      });
      currentForecastData.current = data;
      weekDatesRef.current = data.week_dates;

      // Store overhead data
      setOverheadData({
        labels: weekDatesRef.current,
        accounts_totals: data.accounts_totals,
        payroll_expenses_totals: data.payroll_expenses_totals,
        payroll_expenses_weekly: data.payroll_expenses_weekly,
        fixed_expenses_totals: data.fixed_expenses_totals,
        fixed_expenses_weekly: data.fixed_expenses_weekly,
        misc_expenses_totals: data.misc_expenses_totals,
        misc_expenses_weekly: data.misc_expenses_weekly,
      });

      // Create table data (only current forecast)
      setcurrentForecastLabels(weekDatesRef.current);

      // Get visible snapshots
      const visibleSnapshotIds = Object.keys(visibleLines).filter(k => visibleLines[k]);
      const availableSnapshotIds = visibleSnapshotIds.filter(
        sid => snapshotData[sid] && snapshotData[sid].week_dates
      );

      // Define color families for better visual grouping and distinction
      const colorScheme = {
        working_capital: 'rgb(46, 139, 33)',
        savings: 'rgb(30, 144, 255)',
        currentProjects: {
          expenses: 'rgb(220, 20, 60)', // Crimson (darkest)
          purchaseOrders: 'rgb(255, 69, 0)', // Red-Orange (medium)
          invoices: 'rgb(255, 140, 0)', // Dark Orange (lightest)
          workingCapital: 'rgb(178, 34, 34)', // Fire Brick (working capital)
        },
        anticipatedProjects: {
          expenses: 'rgb(25, 25, 112)', // Midnight Blue (darkest)
          purchaseOrders: 'rgb(65, 105, 225)', // Royal Blue (medium)
          invoices: 'rgb(135, 206, 250)', // Light Sky Blue (lightest)
          workingCapital: 'rgb(72, 61, 139)', // Dark Slate Blue (working capital)
        },
      };

      // Create chart data (current forecast + snapshots)
      const newChartData: ChartData<'line'> = {
        labels: weekDatesRef.current,
        datasets: [
          {
            label: 'Working Capital',
            data: data.working_capital,
            lineColor: colorScheme.working_capital,
            hidden: !lineVisibility.working_capital,
          },
          {
            label: 'Savings',
            data: data.savings_balance,
            lineColor: colorScheme.savings,
            hidden: !lineVisibility.savings_cash,
          },
          {
            label: 'Current Projects Expenses',
            data: data.current_projects_expenses,
            lineColor: colorScheme.currentProjects.expenses,
            hidden: !lineVisibility.current_projects_expenses,
          },
          {
            label: 'Current Projects Purchase Orders',
            data: data.current_projects_purchase_orders,
            lineColor: colorScheme.currentProjects.purchaseOrders,
            hidden: !lineVisibility.current_projects_purchase_orders,
          },
          {
            label: 'Current Projects Invoices',
            data: data.current_projects_invoices,
            lineColor: colorScheme.currentProjects.invoices,
            hidden: !lineVisibility.current_projects_invoices,
          },
          {
            label: 'Anticipated Projects Expenses',
            data: data.anticipated_projects_expenses,
            lineColor: colorScheme.anticipatedProjects.expenses,
            hidden: !lineVisibility.anticipated_projects_expenses,
          },
          {
            label: 'Anticipated Projects Purchase Orders',
            data: data.anticipated_projects_purchase_orders,
            lineColor: colorScheme.anticipatedProjects.purchaseOrders,
            hidden: !lineVisibility.anticipated_projects_purchase_orders,
          },
          {
            label: 'Anticipated Projects Invoices',
            data: data.anticipated_projects_invoices,
            lineColor: colorScheme.anticipatedProjects.invoices,
            hidden: !lineVisibility.anticipated_projects_invoices,
          },
          {
            label: 'Current Projects Working Capital',
            data: data.current_projects_working_capital,
            lineColor: colorScheme.currentProjects.workingCapital,
            hidden: !lineVisibility.current_projects_working_capital,
          },
          {
            label: 'Anticipated Projects Working Capital',
            data: data.anticipated_projects_working_capital,
            lineColor: colorScheme.anticipatedProjects.workingCapital,
            hidden: !lineVisibility.anticipated_projects_working_capital,
          },
        ],
      };

      // If there are visible snapshots, add them to the chart data
      if (availableSnapshotIds.length > 0) {
        // Get all unique dates from current forecast and visible snapshots
        const allDates = new Set<string>();
        weekDatesRef.current.forEach((date: string) => allDates.add(date));
        availableSnapshotIds.forEach(snapshotId => {
          const snapshot = snapshotData[snapshotId];
          if (snapshot?.week_dates) {
            snapshot.week_dates.forEach((date: string) => allDates.add(date));
          }
        });

        // Sort all dates
        const sortedDates = Array.from(allDates).sort();
        newChartData.labels = sortedDates;

        // Update current forecast datasets to align with expanded date range
        newChartData.datasets = newChartData.datasets.map(dataset => ({
          ...dataset,
          data: sortedDates.map((date: string) => {
            const originalIndex = weekDatesRef.current.indexOf(date);
            return originalIndex !== -1 ? dataset.data[originalIndex] : null;
          }),
        }));

        // Add snapshot datasets
        availableSnapshotIds.forEach((snapshotId, index) => {
          const snapshot = snapshotData[snapshotId];
          if (!snapshot) return;

          const snapshotDatasets = createSnapshotDatasets(snapshot, index, lineVisibility);
          snapshotDatasets.forEach(dataset => {
            const newData = sortedDates.map(date => {
              const originalIndex = snapshot.week_dates?.indexOf(date);
              return originalIndex !== -1 ? dataset.data[originalIndex] : null;
            });
            newChartData.datasets.push({
              ...dataset,
              data: newData,
            });
          });
        });
      }

      setChartData(newChartData);

      // Use project data from forecast response instead of separate API calls
      setProjects({
        current: data.current_projects,
        anticipated: data.anticipated_projects.projects.filter((p: Project) => !p.archived),
      });

      // Set accounts data from forecast response
      setAccounts(data.accounts || []);

      // Calculate project data for both types
      if (weekDatesRef.current.length > 0) {
        const [currentProjectsW, currentWeeklyT, currentRunningT] = calculateProjectData(
          data.current_projects
        );
        const [anticipatedProjectsW, anticipatedWeeklyT, anticipatedRunningT] =
          calculateProjectData(
            data.anticipated_projects.projects.filter((p: Project) => !p.archived)
          );

        setProjectsByWeek({
          current: currentProjectsW,
          anticipated: anticipatedProjectsW,
        });
        setWeeklyTotals({
          current: currentWeeklyT,
          anticipated: anticipatedWeeklyT,
        });
        setRunningTotals({
          current: currentRunningT,
          anticipated: anticipatedRunningT,
        });
      }

      message.success('Forecast generated successfully');
    } catch (error: any) {
      message.error('Failed to generate forecast');
    } finally {
      setSubmitting(false);
    }
  };

  const handleSaveSnapshot = async (): Promise<void> => {
    if (!chartData) {
      message.error('No forecast data to save');
      return;
    }
    const name = (document.getElementById('snapshot-name') as HTMLInputElement)?.value;
    if (!name) {
      message.error('Please enter a snapshot name');
      return;
    }
    setSavingSnapshot(true);
    try {
      if (!currentDateRange?.startWeek || !currentDateRange?.endWeek) {
        console.warn('Form values not set, cannot save snapshot');
        return;
      }
      const { data: newSnapshot } = await saveForecastSnapshot({
        id: 0,
        name,
        start_date: currentDateRange?.startWeek,
        end_date: currentDateRange?.endWeek,
      });

      // Add the new snapshot to the existing list
      const updatedSnapshots = [...snapshots, newSnapshot];
      setSnapshots(updatedSnapshots);
      setSnapshotData(prev => ({ ...prev, [newSnapshot.id]: newSnapshot }));
      setVisibleLines(prev => ({ ...prev, [newSnapshot.id]: false }));

      // Clear the input field
      const inputElement = document.getElementById('snapshot-name') as HTMLInputElement;
      if (inputElement) {
        inputElement.value = '';
      }

      message.success('Snapshot saved');
    } catch (error: any) {
      message.error('Failed to save snapshot');
    } finally {
      setSavingSnapshot(false);
    }
  };

  const handleDeleteSnapshot = async (id: string | number): Promise<void> => {
    try {
      await deleteForecastSnapshot(id);

      // Remove the snapshot from the existing list
      const updatedSnapshots = snapshots.filter(s => s.id !== id);
      setSnapshots(updatedSnapshots);

      // Remove from snapshot data and visible lines
      setSnapshotData(prev => {
        const newData = { ...prev };
        delete newData[id];
        return newData;
      });

      setVisibleLines(prev => {
        const newVisibleLines = { ...prev };
        delete newVisibleLines[id];
        return newVisibleLines;
      });

      message.success('Snapshot deleted');
    } catch (error: any) {
      message.error('Failed to delete snapshot');
    }
  };

  const handleLineVisibilityChange = (key: keyof LineVisibility, checked: boolean): void => {
    setLineVisibility(prev => ({ ...prev, [key]: checked }));
    setChartData((prev: ChartData<'line'> | null) => {
      if (!prev) return null;
      return {
        ...prev,
        datasets: prev.datasets.map((d: any) => {
          const label = d.label?.toLowerCase() || '';
          let shouldHide = d.hidden; // Preserve current hidden state by default

          // Handle new project-specific lines
          if (key === 'current_projects_expenses' && label.includes('current projects expenses')) {
            shouldHide = !checked;
          } else if (
            key === 'current_projects_purchase_orders' &&
            label.includes('current projects purchase orders')
          ) {
            shouldHide = !checked;
          } else if (
            key === 'current_projects_invoices' &&
            label.includes('current projects invoices')
          ) {
            shouldHide = !checked;
          } else if (
            key === 'anticipated_projects_expenses' &&
            label.includes('anticipated projects expenses')
          ) {
            shouldHide = !checked;
          } else if (
            key === 'anticipated_projects_purchase_orders' &&
            label.includes('anticipated projects purchase orders')
          ) {
            shouldHide = !checked;
          } else if (
            key === 'anticipated_projects_invoices' &&
            label.includes('anticipated projects invoices')
          ) {
            shouldHide = !checked;
          } else if (
            key === 'current_projects_working_capital' &&
            label.includes('current projects working capital')
          ) {
            shouldHide = !checked;
          } else if (
            key === 'anticipated_projects_working_capital' &&
            label.includes('anticipated projects working capital')
          ) {
            shouldHide = !checked;
          } else if (
            key === 'working_capital' &&
            label.includes('working capital') &&
            !label.includes('projects')
          ) {
            shouldHide = !checked;
          } else if (key === 'savings_cash' && label.includes('savings')) {
            shouldHide = !checked;
          }

          return {
            ...d,
            hidden: shouldHide,
          };
        }),
      };
    });
  };

  const handleSnapshotVisibilityChange = async (
    id: string | number,
    checked: boolean
  ): Promise<void> => {
    const updatedVisibleLines = { ...visibleLines, [id]: checked };
    setVisibleLines(updatedVisibleLines);

    const visibleSnapshotIds = Object.keys(updatedVisibleLines).filter(k => updatedVisibleLines[k]);

    // If no snapshots are visible and no chart data exists, clear the chart
    if (visibleSnapshotIds.length === 0 && !chartData?.datasets?.length) {
      setChartData(null);
      return;
    }

    // Get all unique dates from current forecast and visible snapshots
    const allDates = new Set<string>();
    if (currentForecastData.current?.week_dates) {
      currentForecastData.current?.week_dates.forEach((date: string) => allDates.add(date));
    }
    visibleSnapshotIds.forEach(snapshotId => {
      const snapshot = snapshotData[snapshotId];
      if (snapshot?.week_dates) {
        snapshot.week_dates.forEach((date: string) => allDates.add(date));
      }
    });

    // Sort all dates
    const sortedDates = Array.from(allDates).sort();

    // Create a new chart data object with expanded date range
    const newChartData: ChartData<'line'> = {
      labels: sortedDates,
      datasets: [],
    };

    // Add current forecast datasets if they exist
    if (chartData?.datasets) {
      const currentForecastDataSet = [
        { ...chartData!.datasets[0], data: currentForecastData.current.working_capital },
        { ...chartData!.datasets[1], data: currentForecastData.current.savings_balance },
        {
          ...chartData!.datasets[2],
          data: currentForecastData.current.current_projects_expenses,
        },
        {
          ...chartData!.datasets[3],
          data: currentForecastData.current.current_projects_purchase_orders,
        },
        {
          ...chartData!.datasets[4],
          data: currentForecastData.current.current_projects_invoices,
        },
        {
          ...chartData!.datasets[5],
          data: currentForecastData.current.anticipated_projects_expenses,
        },
        {
          ...chartData!.datasets[6],
          data: currentForecastData.current.anticipated_projects_purchase_orders,
        },
        {
          ...chartData!.datasets[7],
          data: currentForecastData.current.anticipated_projects_invoices,
        },
        {
          ...chartData!.datasets[8],
          data: currentForecastData.current.current_projects_working_capital,
        },
        {
          ...chartData!.datasets[9],
          data: currentForecastData.current.anticipated_projects_working_capital,
        },
      ];
      currentForecastDataSet.forEach(dataset => {
        const newData = sortedDates.map((date: string) => {
          const originalIndex = currentForecastData.current?.week_dates.indexOf(date);
          return originalIndex !== -1 ? dataset.data[originalIndex] : null;
        });
        newChartData.datasets.push({
          ...dataset,
          data: newData,
        });
      });
    }

    // Add visible snapshot datasets
    visibleSnapshotIds.forEach((snapshotId, index) => {
      const snapshot = snapshotData[snapshotId];
      if (!snapshot) return;

      const snapshotDatasets = createSnapshotDatasets(snapshot, index, lineVisibility);
      snapshotDatasets.forEach(dataset => {
        const newData = sortedDates.map((date: string) => {
          const originalIndex = snapshot.week_dates?.indexOf(date);
          return originalIndex !== -1 ? dataset.data[originalIndex] : null;
        });
        newChartData.datasets.push({
          ...dataset,
          data: newData,
        });
      });
    });

    setChartData(newChartData);
  };

  const handleDrop = async (item: any, newWeekDate: string): Promise<void> => {
    // If dropping in the same cell, do nothing
    if (item.weekDate === newWeekDate) {
      return;
    }

    const type = item.project_id in projectsByWeek.anticipated ? 'anticipated' : 'current';
    let updateFunc: any;
    switch (item.type) {
      case 'milestone':
        updateFunc = updateMilestone;
        break;
      case 'projectExpense':
        updateFunc = updateProjectExpense;
        break;
      case 'purchaseOrder':
        updateFunc = updatePurchaseOrder;
        break;
      case 'invoice':
        updateFunc = updateInvoice;
        break;
      default:
        console.error(`Unknown item type: ${item.type}`);
        return;
    }
    const updatedItem = { ...item, date: newWeekDate };
    if (item.type === 'purchaseOrder') {
      updatedItem.issue_date = calculateIssueDate(dayjs(newWeekDate), item.lead_time, item.terms);
      updatedItem.due_date = newWeekDate;
    } else if (item.type === 'invoice') {
      updatedItem.due_date = newWeekDate;
    }
    const originalWeekDate = item.weekDate;

    const moveItem = (prev: ProjectsByWeek): ProjectsByWeek => {
      const newProjectsByWeek = { ...prev };
      const projectData = newProjectsByWeek[item.project_id];
      if (projectData) {
        const oldWeek = projectData.weeklyData[originalWeekDate];
        const newWeek = projectData.weeklyData[newWeekDate] || { total: 0, items: [] };

        // Remove item from original week
        newProjectsByWeek[item.project_id].weeklyData[originalWeekDate] = {
          ...oldWeek,
          items: oldWeek.items.filter(i => i.id !== item.id),
        };

        // Add item to new week
        newProjectsByWeek[item.project_id].weeklyData[newWeekDate] = {
          ...newWeek,
          items: [...newWeek.items, updatedItem],
        };

        // Recalculate totals for both weeks
        const originalWeekItems =
          newProjectsByWeek[item.project_id].weeklyData[originalWeekDate].items;
        const newWeekItems = newProjectsByWeek[item.project_id].weeklyData[newWeekDate].items;

        // Calculate total for original week
        newProjectsByWeek[item.project_id].weeklyData[originalWeekDate].total =
          originalWeekItems.reduce((sum, i) => {
            if (i.type === 'milestone') {
              return sum + i.amount;
            } else if (i.type === 'invoice') {
              return sum - i.amount;
            }
            // Don't include projectExpense or purchaseOrder in totals
            return sum;
          }, 0);

        // Calculate total for new week
        newProjectsByWeek[item.project_id].weeklyData[newWeekDate].total = newWeekItems.reduce(
          (sum, i) => {
            if (i.type === 'milestone') {
              return sum + i.amount;
            } else if (i.type === 'invoice') {
              return sum - i.amount;
            }
            // Don't include projectExpense or purchaseOrder in totals
            return sum;
          },
          0
        );
      }
      return newProjectsByWeek;
    };

    setProjectsByWeek(prev => ({ ...prev, [type]: moveItem(prev[type]) }));

    const updateTotals = (
      projectsByWeek: ProjectsByWeek,
      prevTotals: Record<string, number>,
      prevRunningTotals: Record<string, number>
    ): [Record<string, number>, Record<string, number>] => {
      const newTotals = { ...prevTotals };
      const newRunningTotals = { ...prevRunningTotals };

      // Only consider milestones and invoices for weekly totals
      let amountChange = 0;
      if (item.type === 'milestone') {
        amountChange = item.amount;
      } else if (item.type === 'invoice') {
        amountChange = -item.amount;
      }
      // Don't include projectExpense or purchaseOrder in totals

      newTotals[originalWeekDate] = (newTotals[originalWeekDate] || 0) - amountChange;
      newTotals[newWeekDate] = (newTotals[newWeekDate] || 0) + amountChange;

      let runningTotal = 0;
      Object.keys(newTotals)
        .sort()
        .forEach((week: string) => {
          runningTotal += newTotals[week] || 0;
          newRunningTotals[week] = runningTotal;
        });

      return [newTotals, newRunningTotals];
    };

    requestAnimationFrame(() => {
      const [newWeekly, newRunning] = updateTotals(
        projectsByWeek[type],
        weeklyTotals[type],
        runningTotals[type]
      );
      setWeeklyTotals(prev => ({ ...prev, [type]: newWeekly }));
      setRunningTotals(prev => ({ ...prev, [type]: newRunning }));
    });

    const tagIds = await processTags(updatedItem.tag_names);
    const categoryTagId =
      updatedItem.type === 'projectExpense' ||
      updatedItem.type === 'purchaseOrder' ||
      updatedItem.type === 'invoice'
        ? await processCategoryTag(updatedItem.category_tag_name?.[0] || '')
        : undefined;
    const { tags, tag_names, category_tag_name, ...apiValues } = updatedItem;

    const apiPayload = { ...apiValues, tag_ids: tagIds };
    if (
      (updatedItem.type === 'projectExpense' ||
        updatedItem.type === 'purchaseOrder' ||
        updatedItem.type === 'invoice') &&
      categoryTagId
    ) {
      (apiPayload as any).category_tag_id = categoryTagId;
    }

    await updateFunc(apiPayload);
    updatedItem.tags = allTags.filter(tag => tagIds.includes(tag.id));

    // Set the category_tag object if a category tag was processed
    if (categoryTagId) {
      const categoryTag = allTags.find(tag => tag.id === categoryTagId);
      if (categoryTag) {
        updatedItem.category_tag = categoryTag;
      }
    } else {
      // Clear category_tag if no category was set
      updatedItem.category_tag = null;
    }

    // Process the item description to show relationships
    const project = projects[type].find(p => p.id === updatedItem.project_id);
    if (project) {
      updatedItem.displayDescription = processItemDescription(updatedItem, project);
      updatedItem.displayTitle = processItemTitle(updatedItem);
    }

    const updateState = (
      prevState: Readonly<Record<string, ProjectsByWeek>>
    ): Record<string, ProjectsByWeek> => {
      const newProjectsByWeek = JSON.parse(JSON.stringify(prevState));
      const projectData = newProjectsByWeek[type][updatedItem.project_id];
      if (projectData) {
        Object.values(projectData.weeklyData).forEach((weekData: any) => {
          const itemIndex = weekData.items.findIndex((i: any) => i.id === updatedItem.id);
          if (itemIndex !== -1) {
            weekData.items[itemIndex] = updatedItem;
            // Only include milestones and invoices in totals
            weekData.total = weekData.items.reduce((sum: number, i: any) => {
              if (i.type === 'milestone') {
                return sum + i.amount;
              } else if (i.type === 'invoice') {
                return sum - i.amount;
              }
              // Don't include projectExpense or purchaseOrder in totals
              return sum;
            }, 0);
          }
        });
      }
      return newProjectsByWeek;
    };

    setProjectsByWeek(updateState);

    // Update totals based on the updated projectsByWeek state
    setWeeklyTotals(prevState => {
      const newTotals = JSON.parse(JSON.stringify(prevState));
      // Get the updated projectsByWeek state by applying the same update logic
      const updatedProjectsByWeek = updateState(projectsByWeek);
      Object.keys(newTotals[type]).forEach(weekDate => {
        newTotals[type][weekDate] = Object.values(updatedProjectsByWeek[type]).reduce(
          (sum, pd: any) => sum + (pd.weeklyData[weekDate]?.total || 0),
          0
        );
      });
      return newTotals;
    });

    // Update running totals based on the updated weekly totals
    setRunningTotals(prevState => {
      const newRunningTotals = JSON.parse(JSON.stringify(prevState));
      // Get the updated projectsByWeek state by applying the same update logic
      const updatedProjectsByWeek = updateState(projectsByWeek);

      // Calculate new weekly totals first
      const newWeeklyTotals: Record<string, number> = {};
      Object.keys(weeklyTotals[type]).forEach(weekDate => {
        newWeeklyTotals[weekDate] = Object.values(updatedProjectsByWeek[type]).reduce(
          (sum, pd: any) => sum + (pd.weeklyData[weekDate]?.total || 0),
          0
        );
      });

      // Calculate running totals based on new weekly totals
      let runningTotal = 0;
      Object.keys(newWeeklyTotals)
        .sort()
        .forEach(week => {
          runningTotal += newWeeklyTotals[week] || 0;
          newRunningTotals[type][week] = runningTotal;
        });
      return newRunningTotals;
    });

    if (!currentDateRange?.startWeek || !currentDateRange?.endWeek) {
      console.warn('Form values not set, skipping chart update');
      return;
    }
    const { data } = await generateForecast({
      start_date: currentDateRange.startWeek,
      end_date: currentDateRange.endWeek,
    });
    currentForecastData.current = data;
    setChartData((prev: ChartData<'line'> | null) => ({
      ...prev!,
      datasets: [
        { ...prev!.datasets[0], data: data.working_capital },
        { ...prev!.datasets[1], data: data.savings_balance },
        { ...prev!.datasets[2], data: data.current_projects_expenses },
        { ...prev!.datasets[3], data: data.current_projects_purchase_orders },
        { ...prev!.datasets[4], data: data.current_projects_invoices },
        { ...prev!.datasets[5], data: data.anticipated_projects_expenses },
        { ...prev!.datasets[6], data: data.anticipated_projects_purchase_orders },
        { ...prev!.datasets[7], data: data.anticipated_projects_invoices },
        { ...prev!.datasets[8], data: data.current_projects_working_capital },
        { ...prev!.datasets[9], data: data.anticipated_projects_working_capital },
      ],
    }));
    message.success('Item updated successfully');
  };

  const handleEdit = async (updatedItem: WeeklyItem): Promise<void> => {
    try {
      const type = updatedItem.project_id in projectsByWeek.anticipated ? 'anticipated' : 'current';
      let updateFunc: any;
      switch (updatedItem.type) {
        case 'milestone':
          updateFunc = updateMilestone;
          break;
        case 'projectExpense':
          updateFunc = updateProjectExpense;
          break;
        case 'purchaseOrder':
          updateFunc = updatePurchaseOrder;
          break;
        case 'invoice':
          updateFunc = updateInvoice;
          break;
        default:
          console.error(`Unknown item type: ${updatedItem.type}`);
          return;
      }

      const tagIds = await processTags(updatedItem.tag_names);
      let categoryTagName = '';
      if (Array.isArray(updatedItem.category_tag_name)) {
        categoryTagName = updatedItem.category_tag_name?.[0] || '';
      } else {
        categoryTagName = updatedItem.category_tag_name || '';
      }
      let categoryTagId: number | undefined = undefined;
      if (categoryTagName) {
        categoryTagId = await processCategoryTag(categoryTagName);
      }
      const { tags, tag_names, category_tag_name, ...apiValues } = updatedItem;

      const apiPayload = { ...apiValues, tag_ids: tagIds };
      if (
        (updatedItem.type === 'projectExpense' ||
          updatedItem.type === 'purchaseOrder' ||
          updatedItem.type === 'invoice') &&
        categoryTagId
      ) {
        (apiPayload as any).category_tag_id = categoryTagId;
      }

      await updateFunc(apiPayload);
      updatedItem.tags = allTags.filter(tag => tagIds.includes(tag.id));

      // Set the category_tag object if a category tag was processed
      if (categoryTagId) {
        const categoryTag = allTags.find(tag => tag.id === categoryTagId);
        if (categoryTag) {
          updatedItem.category_tag = categoryTag;
        }
      } else {
        // Clear category_tag if no category was set
        updatedItem.category_tag = null;
      }

      // Process the item description to show relationships
      const project = projects[type].find(p => p.id === updatedItem.project_id);
      if (project) {
        updatedItem.displayDescription = processItemDescription(updatedItem, project);
        updatedItem.displayTitle = processItemTitle(updatedItem);
      }

      const updateState = (
        prevState: Readonly<Record<string, ProjectsByWeek>>
      ): Record<string, ProjectsByWeek> => {
        const newProjectsByWeek = JSON.parse(JSON.stringify(prevState));
        const projectData = newProjectsByWeek[type][updatedItem.project_id];
        if (projectData) {
          Object.values(projectData.weeklyData).forEach((weekData: any) => {
            const itemIndex = weekData.items.findIndex((i: any) => i.id === updatedItem.id);
            if (itemIndex !== -1) {
              weekData.items[itemIndex] = updatedItem;
              // Only include milestones and invoices in totals
              weekData.total = weekData.items.reduce((sum: number, i: any) => {
                if (i.type === 'milestone') {
                  return sum + i.amount;
                } else if (i.type === 'invoice') {
                  return sum - i.amount;
                }
                // Don't include projectExpense or purchaseOrder in totals
                return sum;
              }, 0);
            }
          });
        }
        return newProjectsByWeek;
      };

      setProjectsByWeek(updateState);

      // Update totals based on the updated projectsByWeek state
      setWeeklyTotals(prevState => {
        const newTotals = JSON.parse(JSON.stringify(prevState));
        // Get the updated projectsByWeek state by applying the same update logic
        const updatedProjectsByWeek = updateState(projectsByWeek);
        Object.keys(newTotals[type]).forEach(weekDate => {
          newTotals[type][weekDate] = Object.values(updatedProjectsByWeek[type]).reduce(
            (sum, pd: any) => sum + (pd.weeklyData[weekDate]?.total || 0),
            0
          );
        });
        return newTotals;
      });

      // Update running totals based on the updated weekly totals
      setRunningTotals(prevState => {
        const newRunningTotals = JSON.parse(JSON.stringify(prevState));
        // Get the updated projectsByWeek state by applying the same update logic
        const updatedProjectsByWeek = updateState(projectsByWeek);

        // Calculate new weekly totals first
        const newWeeklyTotals: Record<string, number> = {};
        Object.keys(weeklyTotals[type]).forEach(weekDate => {
          newWeeklyTotals[weekDate] = Object.values(updatedProjectsByWeek[type]).reduce(
            (sum, pd: any) => sum + (pd.weeklyData[weekDate]?.total || 0),
            0
          );
        });

        // Calculate running totals based on new weekly totals
        let runningTotal = 0;
        Object.keys(newWeeklyTotals)
          .sort()
          .forEach(week => {
            runningTotal += newWeeklyTotals[week] || 0;
            newRunningTotals[type][week] = runningTotal;
          });
        return newRunningTotals;
      });

      if (!currentDateRange?.startWeek || !currentDateRange?.endWeek) {
        console.warn('Form values not set, skipping chart update');
        return;
      }
      const { data } = await generateForecast({
        start_date: currentDateRange.startWeek,
        end_date: currentDateRange.endWeek,
      });
      currentForecastData.current = data;
      setChartData((prev: ChartData<'line'> | null) => ({
        ...prev!,
        datasets: [
          { ...prev!.datasets[0], data: data.working_capital },
          { ...prev!.datasets[1], data: data.savings_balance },
          { ...prev!.datasets[2], data: data.current_projects_expenses },
          { ...prev!.datasets[3], data: data.current_projects_purchase_orders },
          { ...prev!.datasets[4], data: data.current_projects_invoices },
          { ...prev!.datasets[5], data: data.anticipated_projects_expenses },
          { ...prev!.datasets[6], data: data.anticipated_projects_purchase_orders },
          { ...prev!.datasets[7], data: data.anticipated_projects_invoices },
          { ...prev!.datasets[8], data: data.current_projects_working_capital },
          { ...prev!.datasets[9], data: data.anticipated_projects_working_capital },
        ],
      }));
      message.success('Item updated successfully');
    } catch (error: any) {
      console.error('Error editing item:', error);
      message.error('Failed to update item');
    }
  };

  const processTags = async (tags: string[] | undefined): Promise<number[]> => {
    if (!organization) {
      message.error('Organization not found');
      return [];
    }
    try {
      if (!tags) {
        return [];
      }
      const tagIds: number[] = [];
      const newTags: Tag[] = [];
      for (const tagName of tags) {
        const existingTag = allTags.find(tag => tag.name === tagName);
        if (existingTag) {
          tagIds.push(existingTag.id);
        } else {
          const trimmedTagName = tagName.trim();
          const response = await createTag({
            id: 0,
            name: trimmedTagName,
            organization_id: organization.id,
          });
          const tagObject = response.data;
          if (tagObject) {
            tagIds.push(tagObject.id);
            newTags.push(tagObject);
          }
        }
      }

      const updatedTags = [...allTags, ...newTags].sort((a, b) => a.name.localeCompare(b.name));
      setAllTags(updatedTags);
      setTagOptions(
        updatedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );

      return tagIds;
    } catch (error) {
      console.error('Error processing tags:', error);
      message.error('Failed to process tags');
      return [];
    }
  };

  const processCategoryTag = async (tagName: string): Promise<number> => {
    if (!organization) {
      message.error('Organization not found');
      return 0;
    }
    const trimmedTagName = tagName?.trim();
    if (!trimmedTagName) {
      return 0;
    }
    try {
      const existingTag = allTags.find(tag => tag.name === trimmedTagName);
      if (existingTag) {
        return existingTag.id;
      } else {
        const response = await createTag({
          id: 0,
          name: trimmedTagName,
          organization_id: organization.id,
        });
        const tagObject = response.data;
        if (tagObject) {
          return tagObject.id;
        }
      }
      return 0;
    } catch (error) {
      console.error('Error processing expense category tag:', error);
      message.error('Failed to process expense category tag');
      return 0;
    }
  };

  const handleAddItem = async (newItem: any): Promise<void> => {
    try {
      const type = newItem.project_id in projectsByWeek.anticipated ? 'anticipated' : 'current';
      let createFunc: any;
      switch (newItem.type) {
        case 'milestone':
          createFunc = createMilestone;
          break;
        case 'projectExpense':
          createFunc = createProjectExpense;
          break;
        case 'purchaseOrder':
          createFunc = addPurchaseOrder;
          break;
        case 'invoice':
          createFunc = addInvoice;
          break;
        default:
          console.error(`Unknown item type: ${newItem.type}`);
          return;
      }

      const tagIds = await processTags(newItem.tag_names);
      let categoryTagName = '';
      if (Array.isArray(newItem.category_tag_name)) {
        categoryTagName = newItem.category_tag_name?.[0] || '';
      } else {
        categoryTagName = newItem.category_tag_name || '';
      }
      let categoryTagId: number | undefined = undefined;
      if (categoryTagName) {
        categoryTagId = await processCategoryTag(categoryTagName);
      }
      const { tags, tag_names, category_tag_name, ...apiValues } = newItem;

      const apiPayload = { ...apiValues, tag_ids: tagIds };
      if (
        (newItem.type === 'projectExpense' ||
          newItem.type === 'purchaseOrder' ||
          newItem.type === 'invoice') &&
        categoryTagId
      ) {
        (apiPayload as any).category_tag_id = categoryTagId;
      }

      const response = await createFunc(apiPayload);
      const createdItem = response.data;
      createdItem.type = newItem.type;

      // Set the category_tag object if a category tag was processed
      if (categoryTagId) {
        const categoryTag = allTags.find(tag => tag.id === categoryTagId);
        if (categoryTag) {
          createdItem.category_tag = categoryTag;
        }
      }

      // Process the item description to show relationships
      const project = projects[type].find(p => p.id === newItem.project_id);
      if (project) {
        createdItem.displayDescription = processItemDescription(createdItem, project);
        createdItem.displayTitle = processItemTitle(createdItem);
      }

      const updateState = (
        prevState: Readonly<Record<string, ProjectsByWeek>>
      ): Record<string, ProjectsByWeek> => {
        const newProjectsByWeek = JSON.parse(JSON.stringify(prevState));
        const projectData = newProjectsByWeek[type][newItem.project_id];
        if (projectData) {
          // For purchase orders, use due_date; for others, use date
          const itemDate =
            newItem.type === 'purchaseOrder' || newItem.type === 'invoice'
              ? newItem.due_date
              : newItem.date;
          const weekDate = findWeekKey(itemDate);
          if (!weekDate) {
            console.error('No week date found for item:', newItem);
            message.warning(
              `Item date ${itemDate} is outside of current forecast range. Item will not affect values in current forecast.`
            );
            return prevState;
          }
          const weekData = projectData.weeklyData[weekDate] || { total: 0, items: [] };

          // Use the created item data instead of newItem to get the correct ID and server-generated fields
          const itemToAdd = {
            ...createdItem,
            type: newItem.type,
            date: itemDate,
            weekDate: weekDate,
            projectId: newItem.project_id,
          };

          weekData.items.push(itemToAdd);

          // Only include milestones and invoices in totals
          weekData.total = weekData.items.reduce((sum: number, i: any) => {
            if (i.type === 'milestone') {
              return sum + i.amount;
            } else if (i.type === 'invoice') {
              return sum - i.amount;
            }
            // Don't include projectExpense or purchaseOrder in totals
            return sum;
          }, 0);
          projectData.weeklyData[weekDate] = weekData;
        }
        return newProjectsByWeek;
      };

      setProjectsByWeek(updateState);

      // Update totals based on the updated projectsByWeek state
      setWeeklyTotals(prevState => {
        const newTotals = JSON.parse(JSON.stringify(prevState));
        // Get the updated projectsByWeek state by applying the same update logic
        const updatedProjectsByWeek = updateState(projectsByWeek);
        Object.keys(newTotals[type]).forEach(weekDate => {
          newTotals[type][weekDate] = Object.values(updatedProjectsByWeek[type]).reduce(
            (sum, pd: any) => sum + (pd.weeklyData[weekDate]?.total || 0),
            0
          );
        });
        return newTotals;
      });

      // Update running totals based on the updated weekly totals
      setRunningTotals(prevState => {
        const newRunningTotals = JSON.parse(JSON.stringify(prevState));
        // Get the updated projectsByWeek state by applying the same update logic
        const updatedProjectsByWeek = updateState(projectsByWeek);

        // Calculate new weekly totals first
        const newWeeklyTotals: Record<string, number> = {};
        Object.keys(weeklyTotals[type]).forEach(weekDate => {
          newWeeklyTotals[weekDate] = Object.values(updatedProjectsByWeek[type]).reduce(
            (sum, pd: any) => sum + (pd.weeklyData[weekDate]?.total || 0),
            0
          );
        });

        // Calculate running totals based on new weekly totals
        let runningTotal = 0;
        Object.keys(newWeeklyTotals)
          .sort()
          .forEach(week => {
            runningTotal += newWeeklyTotals[week] || 0;
            newRunningTotals[type][week] = runningTotal;
          });
        return newRunningTotals;
      });

      if (!currentDateRange?.startWeek || !currentDateRange?.endWeek) {
        console.warn('Form values not set, skipping chart update');
        return;
      }
      const { data } = await generateForecast({
        start_date: currentDateRange.startWeek,
        end_date: currentDateRange.endWeek,
      });
      currentForecastData.current = data;
      setChartData((prev: ChartData<'line'> | null) => ({
        ...prev!,
        datasets: [
          { ...prev!.datasets[0], data: data.working_capital },
          { ...prev!.datasets[1], data: data.savings_balance },
          { ...prev!.datasets[2], data: data.current_projects_expenses },
          { ...prev!.datasets[3], data: data.current_projects_purchase_orders },
          { ...prev!.datasets[4], data: data.current_projects_invoices },
          { ...prev!.datasets[5], data: data.anticipated_projects_expenses },
          { ...prev!.datasets[6], data: data.anticipated_projects_purchase_orders },
          { ...prev!.datasets[7], data: data.anticipated_projects_invoices },
          { ...prev!.datasets[8], data: data.current_projects_working_capital },
          { ...prev!.datasets[9], data: data.anticipated_projects_working_capital },
        ],
      }));
      message.success('Item added successfully');
    } catch (error: any) {
      console.error('Error adding item:', error);
      message.error('Failed to add item');
    }
  };

  const handleDelete = async (item: any, type: string): Promise<void> => {
    try {
      const projectType = item.project_id in projectsByWeek.anticipated ? 'anticipated' : 'current';
      let deleteFunc: any;

      switch (type) {
        case 'milestone':
          deleteFunc = deleteMilestone;
          break;
        case 'projectExpense':
          deleteFunc = deleteProjectExpense;
          break;
        case 'purchaseOrder':
          deleteFunc = deletePurchaseOrder;
          break;
        case 'invoice':
          deleteFunc = deleteInvoice;
          break;
        default:
          console.error(`Unknown item type: ${type}`);
          return;
      }

      await deleteFunc(item.id);

      // Update the state to remove the item
      const updateState = (
        prevState: Readonly<Record<string, ProjectsByWeek>>
      ): Record<string, ProjectsByWeek> => {
        const newProjectsByWeek = JSON.parse(JSON.stringify(prevState));
        const projectData = newProjectsByWeek[projectType][item.project_id];
        if (projectData) {
          const weekDate = item.weekDate || findWeekKey(item.date || item.due_date);
          if (weekDate && projectData.weeklyData[weekDate]) {
            const weekData = projectData.weeklyData[weekDate];
            weekData.items = weekData.items.filter((i: any) => i.id !== item.id);

            // Recalculate totals
            weekData.total = weekData.items.reduce((sum: number, i: any) => {
              if (i.type === 'milestone') {
                return sum + i.amount;
              } else if (i.type === 'invoice') {
                return sum - i.amount;
              }
              return sum;
            }, 0);
          }
        }
        return newProjectsByWeek;
      };

      setProjectsByWeek(updateState);

      // Update totals
      setWeeklyTotals(prevState => {
        const newTotals = JSON.parse(JSON.stringify(prevState));
        const updatedProjectsByWeek = updateState(projectsByWeek);
        Object.keys(newTotals[projectType]).forEach(weekDate => {
          newTotals[projectType][weekDate] = Object.values(
            updatedProjectsByWeek[projectType]
          ).reduce((sum, pd: any) => sum + (pd.weeklyData[weekDate]?.total || 0), 0);
        });
        return newTotals;
      });

      // Update running totals
      setRunningTotals(prevState => {
        const newRunningTotals = JSON.parse(JSON.stringify(prevState));
        const updatedProjectsByWeek = updateState(projectsByWeek);

        const newWeeklyTotals: Record<string, number> = {};
        Object.keys(weeklyTotals[projectType]).forEach(weekDate => {
          newWeeklyTotals[weekDate] = Object.values(updatedProjectsByWeek[projectType]).reduce(
            (sum, pd: any) => sum + (pd.weeklyData[weekDate]?.total || 0),
            0
          );
        });

        let runningTotal = 0;
        Object.keys(newWeeklyTotals)
          .sort()
          .forEach(week => {
            runningTotal += newWeeklyTotals[week] || 0;
            newRunningTotals[projectType][week] = runningTotal;
          });
        return newRunningTotals;
      });

      // Refresh chart data
      if (currentDateRange?.startWeek && currentDateRange?.endWeek) {
        const { data } = await generateForecast({
          start_date: currentDateRange.startWeek,
          end_date: currentDateRange.endWeek,
        });
        currentForecastData.current = data;
        setChartData((prev: ChartData<'line'> | null) => ({
          ...prev!,
          datasets: [
            { ...prev!.datasets[0], data: data.working_capital },
            { ...prev!.datasets[1], data: data.savings_balance },
            { ...prev!.datasets[2], data: data.current_projects_expenses },
            { ...prev!.datasets[3], data: data.current_projects_purchase_orders },
            { ...prev!.datasets[4], data: data.current_projects_invoices },
            { ...prev!.datasets[5], data: data.anticipated_projects_expenses },
            { ...prev!.datasets[6], data: data.anticipated_projects_purchase_orders },
            { ...prev!.datasets[7], data: data.anticipated_projects_invoices },
            { ...prev!.datasets[8], data: data.current_projects_working_capital },
            { ...prev!.datasets[9], data: data.anticipated_projects_working_capital },
          ],
        }));
      }

      message.success('Item deleted successfully');
    } catch (error: any) {
      console.error('Error deleting item:', error);
      message.error('Failed to delete item');
    }
  };

  return (
    <div className='forecast-page'>
      <Typography.Title level={2}>Cashflow Forecast</Typography.Title>
      <Card>
        <ForecastForm
          onSubmit={handleSubmit}
          submitting={submitting}
          initialValues={lastSubmittedValues}
        />
      </Card>

      {/* Legend explaining item types */}
      <Card style={{ marginBottom: 16 }}>
        <Typography.Title level={4}>Item Types Legend</Typography.Title>
        <Row gutter={[16, 8]}>
          <Col span={6}>
            <Row align='middle' gutter={8}>
              <Col>
                <span style={{ fontSize: '16px' }}>💰</span>
              </Col>
              <Col>
                <AntdTag color='success'>Revenue</AntdTag>
              </Col>
              <Col>
                <Typography.Text type='secondary'>Milestones - Money coming in</Typography.Text>
              </Col>
            </Row>
          </Col>
          <Col span={6}>
            <Row align='middle' gutter={8}>
              <Col>
                <span style={{ fontSize: '16px' }}>📝</span>
              </Col>
              <Col>
                <AntdTag color='processing'>Projected</AntdTag>
              </Col>
              <Col>
                <Typography.Text type='secondary'>
                  Project Expenses - Projected spending by category
                </Typography.Text>
              </Col>
            </Row>
          </Col>
          <Col span={6}>
            <Row align='middle' gutter={8}>
              <Col>
                <span style={{ fontSize: '16px' }}>📋</span>
              </Col>
              <Col>
                <AntdTag color='warning'>Expected</AntdTag>
              </Col>
              <Col>
                <Typography.Text type='secondary'>
                  Purchase Orders - Expected firm spending commitments
                </Typography.Text>
              </Col>
            </Row>
          </Col>
          <Col span={6}>
            <Row align='middle' gutter={8}>
              <Col>
                <span style={{ fontSize: '16px' }}>🧾</span>
              </Col>
              <Col>
                <AntdTag color='error'>Debt</AntdTag>
              </Col>
              <Col>
                <Typography.Text type='secondary'>
                  Invoices - Actual debts that will leave the account
                </Typography.Text>
              </Col>
            </Row>
          </Col>
        </Row>
        <Typography.Text
          type='secondary'
          style={{ fontSize: '12px', display: 'block', marginTop: 8 }}
        >
          <strong>Relationships:</strong> Projected Expenses → Expected Purchase Orders → Invoices.
          <strong>Weekly totals only include actual cash flow:</strong> Milestones (money in) and
          Invoices (money out). Projected and Expected items are shown for reference but don't
          affect cash flow totals.
        </Typography.Text>
      </Card>
      <SnapshotsPanel
        snapshots={snapshots}
        visibleLines={visibleLines}
        onVisibilityChange={handleSnapshotVisibilityChange}
        onDelete={handleDeleteSnapshot}
        onSave={handleSaveSnapshot}
        savingSnapshot={savingSnapshot}
        chartData={chartData}
        loading={loading}
      />
      <ForecastChart
        chartData={chartData}
        lineVisibility={lineVisibility}
        onLineVisibilityChange={handleLineVisibilityChange}
        loading={loading}
      />
      <AccountBalances accounts={accounts} />
      <OverheadTable overheadData={overheadData} loading={loading} />
      <ProjectTable
        title='Current Projects by Week'
        projectsByWeek={projectsByWeek.current}
        columnLabels={currentForecastLabels || []}
        weeklyTotals={weeklyTotals.current}
        runningTotals={runningTotals.current}
        loading={loading}
        onDrop={handleDrop}
        onEdit={handleEdit}
        onAddItem={handleAddItem}
        onDelete={handleDelete}
      />
      <ProjectTable
        title='Anticipated Projects by Week'
        projectsByWeek={projectsByWeek.anticipated}
        columnLabels={currentForecastLabels || []}
        weeklyTotals={weeklyTotals.anticipated}
        runningTotals={runningTotals.anticipated}
        loading={loading}
        onDrop={handleDrop}
        onEdit={handleEdit}
        onAddItem={handleAddItem}
        onDelete={handleDelete}
      />
    </div>
  );
};

export default Forecast;
