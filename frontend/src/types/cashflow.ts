import dayjs from 'dayjs';

interface Taggable extends Displayable {
  tag_ids?: number[];
  tags?: Tag[];
  tag_names?: string[];
}

export interface UpsertTag {
  name: string;
  organization_id: number;
  id: number;
  key?: string;
}

interface Displayable {
  displayTitle?: string;
  displayDescription?: string;
}

export interface Tag extends UpsertTag {
  created_at: string;
  updated_at: string;
}

export interface UpsertFixedExpense extends Taggable {
  id: number;
  key?: string;
  date: dayjs.Dayjs;
  description: string;
  amount: number;
  recurrence?: string;
  interval?: number;
}

export interface FixedExpense extends UpsertFixedExpense {}

export interface UpsertMiscExpense extends Taggable, Taggable {
  id: number;
  key?: string;
  date: dayjs.Dayjs;
  description: string;
  amount: number;
}

export interface MiscExpense extends UpsertMiscExpense {}

export interface UpsertPayrollExpense extends Taggable {
  id: number;
  key?: string;
  date: dayjs.Dayjs;
  employee_name: string;
  amount: number;
  recurrence?: string;
  interval?: number;
  notes?: string;
}

export interface PayrollExpense extends UpsertPayrollExpense {}

export interface UpsertProject extends Taggable {
  id: number;
  key?: string;
  name: string;
  description?: string;
  start_date: dayjs.Dayjs;
  duration: number;
  savings_percentage: number;
  status: string;
  archived: boolean;
  active: boolean;
}

export interface Project extends UpsertProject {
  milestones: Milestone[];
  project_expenses: ProjectExpense[];
  purchase_orders: PurchaseOrder[];
  invoices: Invoice[];
}

export interface UpsertMilestone extends Taggable {
  id: number;
  key?: string;
  project_id: number;
  date: dayjs.Dayjs;
  description: string;
  amount: number;
  type: 'milestone';
}

export interface Milestone extends UpsertMilestone {}

export interface UpsertProjectExpense extends Taggable {
  id: number;
  key?: string;
  project_id: number;
  date: dayjs.Dayjs;
  description: string;
  amount: number;
  type: 'projectExpense';
  category_tag_id: number;
  category_tag_name?: string;
}

export interface ProjectExpense extends UpsertProjectExpense {
  category_tag?: Tag;
}

// Project Payment
export interface UpsertProjectPayment extends Taggable {
  id: number;
  key?: string;
  project_id: number;
  date: dayjs.Dayjs;
  amount: number;
  notes?: string;
  project_expense?: ProjectExpense | null;
}

export interface ProjectPayment extends UpsertProjectPayment {}

export interface UpsertPurchaseOrder extends Taggable {
  id: number;
  key?: string;
  po_number: string;
  project_id: number;
  issue_date: dayjs.Dayjs;
  lead_time: number;
  amount: number;
  terms: string;
  description?: string;
  category_tag_id?: number;
  category_tag_name?: string;
  due_date: dayjs.Dayjs;
}

export interface PurchaseOrder extends UpsertPurchaseOrder {
  project_name?: string; // For display purposesmber | null;
  project_expense?: ProjectExpense | null;
  date?: dayjs.Dayjs;
  description?: string;
  category_tag?: Tag;
}

export interface UpsertInvoice extends Taggable {
  id: number;
  key?: string;
  project_id: number;
  invoice_number?: string;
  description?: string;
  amount: number;
  due_date: dayjs.Dayjs;
  purchase_order_id?: number;
  category_tag_id?: number;
  category_tag_name?: string;
}

export interface Invoice extends UpsertInvoice {
  project_name?: string;
  po_number?: string;
  date?: dayjs.Dayjs;
  description?: string;
  category_tag?: Tag;
}

// Invoice

// Forecast Params
export interface ForecastParams {
  start_date: dayjs.Dayjs;
  end_date: dayjs.Dayjs;
}

export interface ForecastFormValues {
  startWeek: dayjs.Dayjs;
  endWeek: dayjs.Dayjs;
}

// Overhead Data
export interface OverheadData {
  labels: string[];
  accounts_totals: number[];
  payroll_expenses_totals: number[];
  payroll_expenses_weekly: number[];
  fixed_expenses_totals: number[];
  fixed_expenses_weekly: number[];
  misc_expenses_totals: number[];
  misc_expenses_weekly: number[];
}

export interface UpsertAccount {
  id: number;
  key?: string;
  name: string;
  account_type: string;
  balance: number;
}

// Account types
export interface Account extends UpsertAccount {}

export interface UpsertForecastSnapshot {
  id: number;
  key?: string;
  name: string;
  timestamp?: string;
  start_date: dayjs.Dayjs;
  end_date: dayjs.Dayjs;
}

export interface ForecastSnapshot extends UpsertForecastSnapshot {
  week_dates: string[];
  working_capital: number[];
  savings_balance: number[];
  current_projects_expenses: number[];
  current_projects_purchase_orders: number[];
  current_projects_invoices: number[];
  anticipated_projects_expenses: number[];
  anticipated_projects_purchase_orders: number[];
  anticipated_projects_invoices: number[];
}

// Consolidated forecast response that includes project data
export interface ForecastResponse {
  week_dates: string[];
  working_capital: number[];
  savings_balance: number[];
  accounts_totals: number[];
  payroll_expenses_totals: number[];
  payroll_expenses_weekly: number[];
  fixed_expenses_totals: number[];
  fixed_expenses_weekly: number[];
  misc_expenses_totals: number[];
  misc_expenses_weekly: number[];
  current_projects_expenses: number[];
  current_projects_purchase_orders: number[];
  current_projects_invoices: number[];
  anticipated_projects_expenses: number[];
  anticipated_projects_purchase_orders: number[];
  anticipated_projects_invoices: number[];
  // Project data (consolidated from separate endpoints)
  current_projects: Project[];
  anticipated_projects: {
    projects: Project[];
    archived: Project[];
  };
  // Account data with names and balances
  accounts: Account[];
}

// Dashboard types
export interface DashboardData {
  total_balance: number;
  monthly_fixed_expenses: number;
  active_projects_value: number;
}

// Weekly Data
export interface WeeklyItem {
  id: number;
  type: 'milestone' | 'projectExpense' | 'purchaseOrder' | 'invoice';
  amount: number;
  date: dayjs.Dayjs;
  project_id: number;
  description: string;
  category_tag_name?: string;
  [key: string]: any;
}

export interface WeeklyData {
  total: number;
  items: WeeklyItem[];
}

export interface ProjectsByWeek {
  [key: number]: {
    project: Project;
    weeklyData: { [weekDate: string]: WeeklyData };
  };
}

export interface Organization {
  id: number;
  key?: string;
  created_at: string;
  updated_at: string;
  name: string;
  subdomain: string;
}
