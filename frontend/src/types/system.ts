// Define ChartData type for backward compatibility
export interface ChartData<TType extends string = string> {
  labels: string[];
  datasets: Array<{
    label?: string;
    data: (number | null)[];
    borderColor?: string;
    backgroundColor?: string;
    borderWidth?: number;
    fill?: boolean;
    hidden?: boolean;
    borderDash?: number[];
    [key: string]: any;
  }>;
}

export interface SimpleTag {
  id: number;
  name: string;
}

export interface LineVisibility {
  working_capital: boolean;
  savings_cash: boolean;
  current_projects_expenses: boolean;
  current_projects_purchase_orders: boolean;
  current_projects_invoices: boolean;
  anticipated_projects_expenses: boolean;
  anticipated_projects_purchase_orders: boolean;
  anticipated_projects_invoices: boolean;
  current_projects_working_capital: boolean;
  anticipated_projects_working_capital: boolean;
}

export interface CustomChartDataset {
  label?: string;
  data: (number | null)[];
  borderColor?: string;
  backgroundColor?: string;
  borderWidth?: number;
  fill?: boolean;
  hidden?: boolean;
  borderDash?: number[];
  snapshotId?: string | number;
  [key: string]: any;
}

export interface ApiResponse<T> {
  data: T;
  status: string;
  message?: string;
}
