import React, { useEffect, useRef } from 'react';
import { Card, Checkbox, Spin } from 'antd';
import * as echarts from 'echarts';
import { ChartData } from '../types';
import dayjs from 'dayjs';
import { formatDateRange } from '../utils/dateFormatting';

interface LineVisibility {
  working_capital: boolean;
  savings_cash: boolean;
  current_projects_expenses: boolean;
  current_projects_purchase_orders: boolean;
  current_projects_invoices: boolean;
  anticipated_projects_expenses: boolean;
  anticipated_projects_purchase_orders: boolean;
  anticipated_projects_invoices: boolean;
  current_projects_working_capital: boolean;
  anticipated_projects_working_capital: boolean;
}

interface ForecastChartProps {
  chartData: ChartData<'line'> | null;
  lineVisibility: LineVisibility;
  onLineVisibilityChange: (key: keyof LineVisibility, checked: boolean) => void;
  loading: boolean;
}

const ForecastChart: React.FC<ForecastChartProps> = ({
  chartData,
  lineVisibility,
  onLineVisibilityChange,
  loading,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // Initialize and update chart
  useEffect(() => {
    if (!chartRef.current) {
      console.log('Chart ref not available');
      return;
    }

    // Initialize or reinitialize chart if needed
    if (!chartInstance.current || chartInstance.current.isDisposed()) {
      chartInstance.current = echarts.init(chartRef.current);
      console.log('Chart initialized or reinitialized');
    }

    // Handle resize
    const handleResize = () => {
      if (chartInstance.current && !chartInstance.current.isDisposed()) {
        chartInstance.current.resize();
      }
    };
    window.addEventListener('resize', handleResize);

    // Update chart based on data
    if (!chartData) {
      console.log('No chart data provided');
      chartInstance.current.clear();
    } else {
      console.log('Updating chart with data:');
      const option = getEChartsOption(chartData, lineVisibility);
      chartInstance.current.setOption(option, true);
      chartInstance.current.resize(); // Force resize to ensure rendering
    }

    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current && !chartInstance.current.isDisposed()) {
        chartInstance.current.dispose();
      }
    };
  }, [chartData, lineVisibility]); // Depend on chartData and lineVisibility

  // Convert Chart.js data format to ECharts format
  const getEChartsOption = (data: ChartData<'line'>, visibility: LineVisibility) => {
    const visibleDatasets = data.datasets.filter(dataset => {
      const label = dataset.label?.toLowerCase() || '';
      if (label.includes('working capital')) return visibility.working_capital;
      if (label.includes('current projects expenses')) return visibility.current_projects_expenses;
      if (label.includes('current projects purchase orders'))
        return visibility.current_projects_purchase_orders;
      if (label.includes('current projects invoices')) return visibility.current_projects_invoices;
      if (label.includes('anticipated projects expenses'))
        return visibility.anticipated_projects_expenses;
      if (label.includes('anticipated projects purchase orders'))
        return visibility.anticipated_projects_purchase_orders;
      if (label.includes('anticipated projects invoices'))
        return visibility.anticipated_projects_invoices;
      if (label.includes('current projects working capital'))
        return visibility.current_projects_working_capital;
      if (label.includes('anticipated projects working capital'))
        return visibility.anticipated_projects_working_capital;
      if (label.includes('savings')) return visibility.savings_cash;
      return true;
    });

    const series = visibleDatasets.map(dataset => {
      const label = dataset.label || '';
      let lineStyle: any = {};
      let symbol = 'circle';
      const symbolSize = 8;

      // Enhanced styling for better visual distinction
      // Current Projects - Red family with distinct patterns and symbols
      if (label.includes('Current Projects Expenses')) {
        lineStyle = { type: 'solid', width: 2 };
        symbol = 'circle';
      } else if (label.includes('Current Projects Purchase Orders')) {
        lineStyle = { type: [12, 4], width: 2, dashOffset: 0 };
        symbol = 'rect';
      } else if (label.includes('Current Projects Invoices')) {
        lineStyle = { type: [6, 3], width: 2, dashOffset: 0 };
        symbol = 'triangle';
      }
      // Anticipated Projects - Blue family with distinct patterns and symbols
      else if (label.includes('Anticipated Projects Expenses')) {
        lineStyle = { type: 'solid', width: 2 };
        symbol = 'circle';
      } else if (label.includes('Anticipated Projects Purchase Orders')) {
        lineStyle = { type: [12, 4], width: 2, dashOffset: 0 };
        symbol = 'rect';
      } else if (label.includes('Anticipated Projects Invoices')) {
        lineStyle = { type: [6, 3], width: 2, dashOffset: 0 };
        symbol = 'triangle';
      }
      // Working Capital and Savings - Green family
      else if (label.includes('Working Capital')) {
        lineStyle = { type: 'solid', width: 2 };
        symbol = 'diamond';
      } else if (label.includes('Savings')) {
        lineStyle = { type: [8, 4], width: 2, dashOffset: 0 };
        symbol = 'diamond';
      }
      // Fallback for other lines
      else {
        lineStyle = { type: 'solid', width: 2 };
        symbol = 'circle';
      }

      return {
        name: label,
        type: 'line',
        data: dataset.data,
        showSymbol: true,
        symbol,
        symbolSize,
        lineStyle,
        itemStyle: {
          color: dataset.lineColor as string,
        },
        emphasis: {
          focus: 'series',
          lineStyle: {
            width: lineStyle.width + 1,
          },
        },
      };
    });

    return {
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: false,
          },
        },
      },
      // dataZoom,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        },
        formatter: (params: any) => {
          const startDate = dayjs(params[0].axisValue);
          const endDate = startDate.day(6);
          let result = `${formatDateRange(startDate, endDate)}<br/>`;
          params.forEach((param: any) => {
            const value = param.value?.toLocaleString() ?? ' - ';
            result += `${param.seriesName}: $${value}<br/>`;
          });
          return result;
        },
      },
      axisPointer: {
        link: { xAxisIndex: 'all' },
        label: {
          backgroundColor: '#777',
        },
      },
      legend: {
        data: visibleDatasets
          .map(dataset => dataset.label)
          .sort((a, b) => (a || '').localeCompare(b || '')),
        top: 'bottom',
        selectedMode: false,
        selected: visibleDatasets.reduce((acc, dataset) => {
          acc[dataset.label || ''] = true;
          return acc;
        }, {} as Record<string, boolean>),
      },
      grid: {
        left: '3%',
        right: '4%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: data.labels,
        axisTick: {
          show: true,
          alignWithLabel: true,
        },
        axisLabel: {
          autoRotate: true,
          interval: 'auto',
          formatter: (value: string) => {
            const startDate = dayjs(value);
            const endDate = startDate.day(6);
            return formatDateRange(startDate, endDate);
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: (value: number) => `$${value.toLocaleString()}`,
        },
      },
      series,
    };
  };

  return (
    <Card title='Forecast Chart'>
      <div style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 8 }}>
          <strong>Forecasted Cash Flow:</strong>
          {(
            [
              { key: 'working_capital', label: 'Working Capital' },
              { key: 'savings_cash', label: 'Savings' },
              {
                key: 'current_projects_working_capital',
                label: 'Current Projects Working Capital',
              },
              {
                key: 'anticipated_projects_working_capital',
                label: 'Anticipated Projects Working Capital',
              },
            ] as const
          ).map(obj => (
            <Checkbox
              key={obj.key}
              checked={lineVisibility[obj.key]}
              onChange={e => onLineVisibilityChange(obj.key, e.target.checked)}
              style={{ marginLeft: 8 }}
            >
              {obj.label}
            </Checkbox>
          ))}
        </div>
        <div style={{ marginBottom: 8 }}>
          <strong>Current Projects:</strong>
          {(
            [
              'current_projects_expenses',
              'current_projects_purchase_orders',
              'current_projects_invoices',
            ] as const
          ).map(key => (
            <Checkbox
              key={key}
              checked={lineVisibility[key]}
              onChange={e => onLineVisibilityChange(key, e.target.checked)}
              style={{ marginLeft: 8 }}
            >
              {key.split('_').slice(2).join(' ')}
            </Checkbox>
          ))}
        </div>
        <div>
          <strong>Anticipated Projects:</strong>
          {(
            [
              'anticipated_projects_expenses',
              'anticipated_projects_purchase_orders',
              'anticipated_projects_invoices',
            ] as const
          ).map(key => (
            <Checkbox
              key={key}
              checked={lineVisibility[key]}
              onChange={e => onLineVisibilityChange(key, e.target.checked)}
              style={{ marginLeft: 8 }}
            >
              {key.split('_').slice(2).join(' ')}
            </Checkbox>
          ))}
        </div>
      </div>
      <div style={{ height: 400, position: 'relative' }}>
        {loading ? (
          <Spin style={{ position: 'absolute', top: '50%', left: '50%' }} />
        ) : (
          <div ref={chartRef} style={{ width: '100%', height: '100%', minHeight: '400px' }} />
        )}
      </div>
    </Card>
  );
};

export default ForecastChart;
